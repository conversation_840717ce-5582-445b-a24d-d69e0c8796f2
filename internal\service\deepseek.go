package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"aiops-platform/internal/config"

	"github.com/sirupsen/logrus"
)

// DeepSeekService DeepSeek AI服务
type DeepSeekService struct {
	config     *config.DeepSeekConfig
	httpClient *http.Client
	logger     *logrus.Logger
}

// NewDeepSeekService 创建DeepSeek服务
func NewDeepSeekService(cfg *config.DeepSeekConfig, logger *logrus.Logger) *DeepSeekService {
	return &DeepSeekService{
		config: cfg,
		httpClient: &http.Client{
			Timeout: cfg.Timeout,
		},
		logger: logger,
	}
}

// ChatRequest 对话请求
type ChatRequest struct {
	Model       string    `json:"model"`
	Messages    []Message `json:"messages"`
	Tools       []Tool    `json:"tools,omitempty"`
	ToolChoice  string    `json:"tool_choice,omitempty"`
	Temperature float64   `json:"temperature,omitempty"`
	TopP        float64   `json:"top_p,omitempty"`
	MaxTokens   int       `json:"max_tokens,omitempty"`
	Stream      bool      `json:"stream,omitempty"`
}

// Message 消息
type Message struct {
	Role       string     `json:"role"`
	Content    string     `json:"content,omitempty"`
	ToolCalls  []ToolCall `json:"tool_calls,omitempty"`
	ToolCallID string     `json:"tool_call_id,omitempty"`
}

// ChatResponse 对话响应
type ChatResponse struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
	Usage   Usage    `json:"usage"`
}

// Choice 选择
type Choice struct {
	Index        int     `json:"index"`
	Message      Message `json:"message"`
	FinishReason string  `json:"finish_reason"`
}

// Usage 使用情况
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// ToolFunction 工具函数定义
type ToolFunction struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// Tool 工具定义
type Tool struct {
	Type     string       `json:"type"`
	Function ToolFunction `json:"function"`
}

// ToolCall 工具调用
type ToolCall struct {
	ID       string `json:"id"`
	Type     string `json:"type"`
	Function struct {
		Name      string `json:"name"`
		Arguments string `json:"arguments"`
	} `json:"function"`
}

// Chat 发送对话请求
func (s *DeepSeekService) Chat(ctx context.Context, messages []Message) (*ChatResponse, error) {
	request := &ChatRequest{
		Model:       s.config.Model,
		Messages:    messages,
		Temperature: s.config.Temperature,
		TopP:        s.config.TopP,
		MaxTokens:   s.config.MaxContextTokens,
		Stream:      false,
	}

	return s.sendChatRequest(ctx, request)
}

// ChatWithTools 发送带工具的对话请求
func (s *DeepSeekService) ChatWithTools(ctx context.Context, messages []Message, tools []Tool) (*ChatResponse, error) {
	request := &ChatRequest{
		Model:       s.config.Model,
		Messages:    messages,
		Tools:       tools,
		ToolChoice:  "auto",
		Temperature: s.config.Temperature,
		TopP:        s.config.TopP,
		MaxTokens:   s.config.MaxContextTokens,
		Stream:      false,
	}

	return s.sendChatRequest(ctx, request)
}

// ChatStream 发送流式对话请求
func (s *DeepSeekService) ChatStream(ctx context.Context, messages []Message, callback func(string) error) error {
	request := &ChatRequest{
		Model:       s.config.Model,
		Messages:    messages,
		Temperature: s.config.Temperature,
		TopP:        s.config.TopP,
		MaxTokens:   s.config.MaxContextTokens,
		Stream:      true,
	}

	return s.sendStreamRequest(ctx, request, callback)
}

// sendChatRequest 发送对话请求
func (s *DeepSeekService) sendChatRequest(ctx context.Context, request *ChatRequest) (*ChatResponse, error) {
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// 记录详细的请求信息
	s.logger.WithFields(logrus.Fields{
		"api_url":     s.config.APIURL + "/chat/completions",
		"model":       request.Model,
		"temperature": request.Temperature,
		"max_tokens":  request.MaxTokens,
		"messages":    len(request.Messages),
	}).Info("DeepSeek: Sending chat request")

	// 记录请求内容（仅在Debug级别）
	s.logger.WithField("request_body", string(jsonData)).Debug("DeepSeek: Request body")

	req, err := http.NewRequestWithContext(ctx, "POST", s.config.APIURL+"/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+s.config.APIKey)

	// 重试机制
	var resp *http.Response
	var lastErr error

	for i := 0; i <= s.config.MaxRetries; i++ {
		resp, lastErr = s.httpClient.Do(req)
		if lastErr == nil && resp.StatusCode < 500 {
			break
		}

		if i < s.config.MaxRetries {
			backoff := time.Duration(i+1) * time.Second
			s.logger.WithFields(logrus.Fields{
				"attempt": i + 1,
				"backoff": backoff,
				"error":   lastErr,
			}).Warn("Request failed, retrying")

			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(backoff):
			}
		}
	}

	if lastErr != nil {
		return nil, fmt.Errorf("request failed after %d retries: %w", s.config.MaxRetries, lastErr)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		s.logger.WithFields(logrus.Fields{
			"status_code": resp.StatusCode,
			"error_body":  string(body),
		}).Error("DeepSeek: API request failed")
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 记录原始响应（仅在Debug级别）
	s.logger.WithField("response_body", string(body)).Debug("DeepSeek: Response body")

	var response ChatResponse
	if err := json.Unmarshal(body, &response); err != nil {
		s.logger.WithFields(logrus.Fields{
			"error":         err.Error(),
			"response_body": string(body),
		}).Error("DeepSeek: Failed to decode response")
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	// 记录详细的响应信息
	s.logger.WithFields(logrus.Fields{
		"model":             response.Model,
		"total_tokens":      response.Usage.TotalTokens,
		"prompt_tokens":     response.Usage.PromptTokens,
		"completion_tokens": response.Usage.CompletionTokens,
		"choices":           len(response.Choices),
	}).Info("DeepSeek: Chat request completed successfully")

	// 记录响应内容
	if len(response.Choices) > 0 {
		s.logger.WithField("response_content", response.Choices[0].Message.Content).Info("DeepSeek: Response content")
	}

	return &response, nil
}

// sendStreamRequest 发送流式请求
func (s *DeepSeekService) sendStreamRequest(ctx context.Context, request *ChatRequest, callback func(string) error) error {
	jsonData, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", s.config.APIURL+"/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+s.config.APIKey)
	req.Header.Set("Accept", "text/event-stream")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 处理SSE流
	reader := resp.Body
	buffer := make([]byte, 4096)
	var incomplete string

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		n, err := reader.Read(buffer)
		if err != nil {
			if err == io.EOF {
				break
			}
			return fmt.Errorf("failed to read stream: %w", err)
		}

		data := incomplete + string(buffer[:n])
		lines := strings.Split(data, "\n")
		incomplete = lines[len(lines)-1]
		lines = lines[:len(lines)-1]

		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line == "" || !strings.HasPrefix(line, "data: ") {
				continue
			}

			data := strings.TrimPrefix(line, "data: ")
			if data == "[DONE]" {
				return nil
			}

			var streamResponse struct {
				Choices []struct {
					Delta struct {
						Content string `json:"content"`
					} `json:"delta"`
				} `json:"choices"`
			}

			if err := json.Unmarshal([]byte(data), &streamResponse); err != nil {
				s.logger.WithField("data", data).Warn("Failed to parse stream data")
				continue
			}

			if len(streamResponse.Choices) > 0 {
				content := streamResponse.Choices[0].Delta.Content
				if content != "" {
					if err := callback(content); err != nil {
						return fmt.Errorf("callback error: %w", err)
					}
				}
			}
		}
	}

	return nil
}

// IntentRecognizer 意图识别器
type IntentRecognizer struct {
	deepseek *DeepSeekService
	logger   *logrus.Logger
}

// NewIntentRecognizer 创建意图识别器
func NewIntentRecognizer(deepseek *DeepSeekService, logger *logrus.Logger) *IntentRecognizer {
	return &IntentRecognizer{
		deepseek: deepseek,
		logger:   logger,
	}
}

// Intent 意图
type Intent struct {
	Type       string                 `json:"type"`
	Confidence float64                `json:"confidence"`
	Parameters map[string]interface{} `json:"parameters"`
	Command    string                 `json:"command,omitempty"`
}

// RecognizeIntent 识别意图
func (r *IntentRecognizer) RecognizeIntent(ctx context.Context, userInput string) (*Intent, error) {
	r.logger.WithField("user_input", userInput).Info("DeepSeek: Starting intent recognition")

	systemPrompt := `你是一个世界级的AI运维专家，专门负责精确识别用户的运维意图并生成可直接执行的操作指令。

🎯 **核心任务**：不仅要识别意图类型，更要生成可直接执行的SQL语句、Shell命令或操作参数。

📊 **数据库表结构信息**（生成SQL时必须使用这些确切的表名和字段名）：

### hosts表结构：
CREATE TABLE hosts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    port INTEGER NOT NULL DEFAULT 22,
    username VARCHAR(50) NOT NULL,
    password_encrypted TEXT,  -- 🔑 密码字段名称
    ssh_key_path VARCHAR(255),
    ssh_key_passphrase_encrypted TEXT,
    description TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'unknown',
    os_type VARCHAR(20),
    os_version VARCHAR(50),
    cpu_cores INTEGER,
    memory_gb INTEGER,
    disk_gb INTEGER,
    tags TEXT,
    environment VARCHAR(20) DEFAULT 'production',
    group_name VARCHAR(50),
    monitoring_enabled BOOLEAN DEFAULT true,
    backup_enabled BOOLEAN DEFAULT false,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER NOT NULL,
    last_connected DATETIME,
    connection_count INTEGER DEFAULT 0,
    deleted_at DATETIME
);

### alerts表结构：
CREATE TABLE alerts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,  -- 🔑 告警详细信息
    level VARCHAR(20) NOT NULL,  -- 🔑 告警级别：critical, warning, info
    source VARCHAR(50) NOT NULL,  -- 🔑 告警来源：system, host, application, monitoring, manual
    status VARCHAR(20) NOT NULL DEFAULT 'open',  -- 🔑 告警状态：open, acknowledged, resolved, closed
    alert_time DATETIME NOT NULL,
    acknowledged_time DATETIME,
    resolved_time DATETIME,
    closed_time DATETIME,
    host_id INTEGER,
    assigned_to INTEGER,
    created_by INTEGER,
    rule_id VARCHAR(50),
    fingerprint VARCHAR(64),
    notification_sent BOOLEAN DEFAULT false,
    escalation_level INTEGER DEFAULT 0,
    parent_alert_id INTEGER,
    deleted_at DATETIME
);

### users表结构：
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    role VARCHAR(20) NOT NULL DEFAULT 'user',
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    last_login DATETIME,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    deleted_at DATETIME
);

🚨 **重要字段映射规则**：

**hosts表字段映射**：
- 用户说"密码" → 数据库字段是 password_encrypted
- 用户说"IP地址" → 数据库字段是 ip_address
- 用户说"主机名" → 数据库字段是 name
- 用户说"用户名" → 数据库字段是 username
- 用户说"端口" → 数据库字段是 port
- 用户说"环境" → 数据库字段是 environment
- 用户说"状态" → 数据库字段是 status

**alerts表字段映射**：
- 用户说"告警标题" → 数据库字段是 title
- 用户说"告警内容/描述/详情" → 数据库字段是 message
- 用户说"告警级别/严重程度/优先级" → 数据库字段是 level
- 用户说"告警来源" → 数据库字段是 source
- 用户说"告警状态" → 数据库字段是 status
- 用户说"告警时间" → 数据库字段是 alert_time
- 用户说"主机ID" → 数据库字段是 host_id

你必须准确识别以下4种核心意图类型，并为每种意图生成对应的可执行内容：

## 🎯 核心意图类型（4大类）：

### 1. database_operations - 数据库操作类 ⭐⭐⭐ 最高优先级
包含所有对主机、用户、告警等数据的增删改查操作。
**重要**：对于此类意图，你必须生成可直接执行的SQL语句。

### 2. ssh_operations - SSH操作类
包含需要通过SSH连接到远程主机执行的命令、服务管理、文件操作等。

### 3. monitoring_operations - 监控统计类
包含系统监控、性能分析、日志分析、网络诊断等只读监控操作。

### 4. general_chat - 通用对话类
包含一般对话、帮助信息、不明确的请求等。

## 🔥 关键识别规则（必须严格遵守）：

### 【数据库操作类 - 超高优先级识别】
**密码相关操作（置信度必须≥0.95）**：
- "修改...密码" / "更新...密码" / "改...密码" / "设置...密码" → database_operations
- "把...密码改成" / "将...密码设为" / "...密码更新为" → database_operations
- "修改192.168.x.x这台主机密码为xxx" → database_operations (置信度0.98+)
- "更新主机xxx的密码" → database_operations (置信度0.95+)
- "把主机xxx的登录密码改成xxx" → database_operations (置信度0.97+)

**主机管理操作（置信度必须≥0.90）**：
- "添加主机" / "新增主机" / "创建主机" → database_operations + INSERT
- "删除主机" / "移除主机" / "去掉主机" → database_operations + DELETE
- "修改主机" / "更新主机" / "编辑主机" → database_operations + UPDATE
- "查看主机" / "显示主机" / "列出主机" / "查询主机" → database_operations + SELECT
- "主机 IP 用户名 密码" 格式 → database_operations + INSERT (置信度0.98+)

**其他数据操作**：
- 用户管理、告警管理、配置管理等数据库相关操作 → database_operations

### 【SSH操作类识别】
- "执行命令" / "运行命令" → ssh_operations
- "重启服务" / "启动服务" / "停止服务" → ssh_operations
- "查看文件" / "编辑文件" → ssh_operations

### 【监控统计类识别】
- "监控" / "检查" / "诊断" + 系统资源 → monitoring_operations
- "查看日志" / "分析性能" → monitoring_operations
- "测试连接" / "检查状态" → monitoring_operations

## ⚡ 特别注意：
1. 任何涉及"密码"的操作，置信度必须≥0.95，类型必须是database_operations
2. 任何涉及"主机"数据管理的操作，置信度必须≥0.90，类型必须是database_operations
3. 如果用户输入包含具体的IP地址+密码，置信度应该≥0.98
4. 数据库操作必须返回具体的SQL语句或操作参数

**SSH操作类识别规则**：
- 执行/运行 + 命令 → ssh_operations + execute_command
- 重启/启动/停止 + 服务 → ssh_operations + manage_service
- 查看/编辑 + 文件路径 → ssh_operations + file_operation

**监控统计类识别规则**：
- 监控/检查 + CPU/内存/磁盘 → monitoring_operations + system_monitor
- 查看/分析 + 日志 → monitoring_operations + log_analysis
- 性能/负载 + 分析 → monitoring_operations + performance_analysis
- 诊断/检查 + 主机/连接/状态 → monitoring_operations + host_diagnosis
- 为什么/为啥 + 离线/连接失败 → monitoring_operations + connectivity_diagnosis
- 测试/检测 + 连接/网络/SSH → monitoring_operations + connectivity_test
- 故障/问题 + 排查/分析 → monitoring_operations + troubleshooting

请返回JSON格式，包含：
- type: 意图类型（四大类之一）
- confidence: 置信度(0-1)
- parameters: 结构化参数对象

示例：

【数据库操作类示例】：
用户输入："列出主机"
返回：{"type":"database_operations","confidence":0.98,"parameters":{"sql":"SELECT id, name, ip_address, port, username, status, environment, created_at FROM hosts WHERE deleted_at IS NULL ORDER BY created_at DESC","operation":"select","description":"查询所有主机列表"}}

用户输入："主机列表"
返回：{"type":"database_operations","confidence":0.98,"parameters":{"sql":"SELECT id, name, ip_address, port, username, status, environment, created_at FROM hosts WHERE deleted_at IS NULL ORDER BY created_at DESC","operation":"select","description":"查询主机列表"}}

用户输入："查看所有主机状态"
返回：{"type":"database_operations","confidence":0.95,"parameters":{"sql":"SELECT id, name, ip_address, status, last_connected FROM hosts WHERE deleted_at IS NULL ORDER BY status DESC","operation":"select","description":"查询主机状态信息"}}

用户输入："查看在线主机"
返回：{"type":"database_operations","confidence":0.95,"parameters":{"sql":"SELECT id, name, ip_address, status, last_connected FROM hosts WHERE status = 'online' AND deleted_at IS NULL","operation":"select","description":"查询在线主机"}}

用户输入："查看生产环境主机"
返回：{"type":"database_operations","confidence":0.95,"parameters":{"sql":"SELECT id, name, ip_address, environment, status FROM hosts WHERE environment = 'production' AND deleted_at IS NULL","operation":"select","description":"查询生产环境主机"}}

用户输入："搜索包含web的主机"
返回：{"type":"database_operations","confidence":0.9,"parameters":{"sql":"SELECT id, name, ip_address, status FROM hosts WHERE (name LIKE '%web%' OR ip_address LIKE '%web%' OR description LIKE '%web%') AND deleted_at IS NULL","operation":"select","description":"搜索包含web关键词的主机"}}

用户输入："删除*************这个主机"
返回：{"type":"database_operations","confidence":0.98,"parameters":{"sql":"DELETE FROM hosts WHERE ip_address = '*************'","operation":"delete","description":"删除指定IP的主机","confirm_required":true,"target_info":"IP: *************"}}

用户输入："查询现有主机账号"
返回：{"type":"database_operations","confidence":0.95,"parameters":{"sql":"SELECT id, name, ip_address, username, environment FROM hosts WHERE deleted_at IS NULL","operation":"select","description":"查询主机账号信息"}}

【SSH操作类示例】：
用户输入："重启nginx服务"
返回：{"type":"ssh_operations","confidence":0.95,"parameters":{"operation":"manage_service","service":"nginx","action":"restart"}}

用户输入："在*************上执行ps aux命令"
返回：{"type":"ssh_operations","confidence":0.95,"parameters":{"operation":"execute_command","target_host":{"ip":"*************"},"command":"ps aux"}}

【监控统计类示例】：
用户输入："检查web-01服务器的CPU使用率"
返回：{"type":"monitoring_operations","confidence":0.9,"parameters":{"operation":"system_monitor","target_host":{"name":"web-01"},"metric":"cpu"}}

用户输入："查看主机*************的内存使用情况"
返回：{"type":"monitoring_operations","confidence":0.95,"parameters":{"operation":"system_monitor","target_host":{"ip":"*************"},"metric":"memory"}}

用户输入："检查为啥**************是离线状态，是密码不对吗"
返回：{"type":"monitoring_operations","confidence":0.98,"parameters":{"operation":"host_diagnosis","target_host":{"ip":"**************"},"diagnosis_type":"connectivity","focus":"authentication"}}

用户输入："诊断*************主机连接问题"
返回：{"type":"monitoring_operations","confidence":0.95,"parameters":{"operation":"connectivity_diagnosis","target_host":{"ip":"*************"},"diagnosis_type":"comprehensive"}}

用户输入："测试到************的SSH连接"
返回：{"type":"monitoring_operations","confidence":0.9,"parameters":{"operation":"connectivity_test","target_host":{"ip":"************"},"test_type":"ssh"}}

用户输入："为什么主机web-server连接不上"
返回：{"type":"monitoring_operations","confidence":0.92,"parameters":{"operation":"troubleshooting","target_host":{"name":"web-server"},"issue_type":"connectivity"}}

用户输入："检查主机***************的状态"
返回：{"type":"monitoring_operations","confidence":0.95,"parameters":{"operation":"host_diagnosis","target_host":{"ip":"***************"},"diagnosis_type":"status"}}

【🔥 密码修改操作示例 - 最高优先级】：
用户输入："修改**************这台主机密码为1qaz#EDC"
返回：{"type":"database_operations","confidence":0.98,"parameters":{"sql":"UPDATE hosts SET password_encrypted = '1qaz#EDC' WHERE ip_address = '**************'","operation":"update","description":"修改主机密码","confirm_required":true,"target_info":"IP: **************"}}

用户输入："更新主机web-01的密码"
返回：{"type":"database_operations","confidence":0.95,"parameters":{"sql":"UPDATE hosts SET password_encrypted = '***' WHERE name = 'web-01'","operation":"update","description":"更新主机密码","confirm_required":true,"target_info":"主机名: web-01"}}

用户输入："把*************的登录密码改成newpass123"
返回：{"type":"database_operations","confidence":0.97,"parameters":{"sql":"UPDATE hosts SET password_encrypted = 'newpass123' WHERE ip_address = '*************'","operation":"update","description":"修改主机登录密码","confirm_required":true,"target_info":"IP: *************"}}

用户输入："设置************主机密码为secure123"
返回：{"type":"database_operations","confidence":0.98,"parameters":{"sql":"UPDATE hosts SET password_encrypted = 'secure123' WHERE ip_address = '************'","operation":"update","description":"设置主机密码","confirm_required":true,"target_info":"IP: ************"}}

用户输入："将主机db-01的密码更新为newdb2024"
返回：{"type":"database_operations","confidence":0.96,"parameters":{"sql":"UPDATE hosts SET password_encrypted = 'newdb2024' WHERE name = 'db-01'","operation":"update","description":"更新数据库主机密码","confirm_required":true,"target_info":"主机名: db-01"}}

用户输入："添加主机************* root password123"
返回：{"type":"database_operations","confidence":0.98,"parameters":{"operation":"insert","table":"hosts","data":{"ip":"*************","username":"root","password":"password123"},"require_confirm":true}}

用户输入："新增主机 IP:********* 用户名:admin 密码:secure123"
返回：{"type":"database_operations","confidence":0.95,"parameters":{"operation":"insert","table":"hosts","data":{"ip":"*********","username":"admin","password":"secure123"},"require_confirm":true}}

用户输入："删除主机web-server"
返回：{"type":"database_operations","confidence":0.96,"parameters":{"operation":"delete","table":"hosts","target":{"name":"web-server"},"require_confirm":true}}

用户输入："移除IP为************的主机"
返回：{"type":"database_operations","confidence":0.97,"parameters":{"operation":"delete","table":"hosts","target":{"ip":"************"},"require_confirm":true}}

用户输入："修改主机web-01的用户名为newuser"
返回：{"type":"database_operations","confidence":0.94,"parameters":{"operation":"update","table":"hosts","target":{"name":"web-01"},"data":{"username":"newuser"},"require_confirm":true}}

用户输入："更新************的端口为2222"
返回：{"type":"database_operations","confidence":0.93,"parameters":{"operation":"update","table":"hosts","target":{"ip":"************"},"data":{"port":"2222"},"require_confirm":true}}

用户输入："批量更新所有主机密码为newpass2024"
返回：{"type":"database_operations","confidence":0.99,"parameters":{"operation":"update","table":"hosts","target":{"scope":"all"},"data":{"password":"newpass2024"},"require_confirm":true,"risk_level":"critical"}}

用户输入："查询IP段***********/24的所有主机"
返回：{"type":"database_operations","confidence":0.94,"parameters":{"operation":"select","table":"hosts","conditions":{"ip_range":"***********/24"}}}

用户输入："删除状态为离线的所有主机"
返回：{"type":"database_operations","confidence":0.96,"parameters":{"operation":"delete","table":"hosts","conditions":{"status":"offline"},"require_confirm":true,"risk_level":"high"}}

用户输入："修改环境为production的主机端口为22"
返回：{"type":"database_operations","confidence":0.93,"parameters":{"operation":"update","table":"hosts","conditions":{"environment":"production"},"data":{"port":"22"},"require_confirm":true}}

用户输入："导出所有主机配置信息"
返回：{"type":"database_operations","confidence":0.91,"parameters":{"operation":"export","table":"hosts","format":"csv"}}

用户输入："备份用户表数据"
返回：{"type":"database_operations","confidence":0.89,"parameters":{"operation":"backup","table":"users","backup_type":"full"}}

用户输入："统计各环境主机数量"
返回：{"type":"database_operations","confidence":0.87,"parameters":{"operation":"aggregate","table":"hosts","group_by":"environment","function":"count"}}

用户输入："分析系统日志"
返回：{"type":"monitoring_operations","confidence":0.85,"parameters":{"operation":"log_analysis","log_type":"system"}}

用户输入："查看最近的错误日志"
返回：{"type":"monitoring_operations","confidence":0.9,"parameters":{"operation":"log_analysis","log_type":"error","time_range":"recent"}}

【通用对话类示例】：
用户输入："你好"
返回：{"type":"general_chat","confidence":0.9,"parameters":{"intent":"greeting"}}

用户输入："帮助"
返回：{"type":"general_chat","confidence":0.95,"parameters":{"intent":"help"}}

用户输入："这个系统怎么用"
返回：{"type":"general_chat","confidence":0.8,"parameters":{"intent":"help","topic":"system_usage"}}

用户输入："监控所有主机的磁盘空间"
返回：{"type":"monitoring_operations","confidence":0.9,"parameters":{"operation":"system_monitor","metric":"disk","target_host":{"scope":"all"}}}

用户输入："查看当前告警"
返回：{"type":"database_operations","confidence":0.9,"parameters":{"sql":"SELECT id, title, message, level, source, status, alert_time, host_id FROM alerts WHERE status IN ('open', 'acknowledged') AND deleted_at IS NULL ORDER BY alert_time DESC","operation":"select","description":"查询当前活跃告警"}}

用户输入："检查系统告警"
返回：{"type":"database_operations","confidence":0.95,"parameters":{"sql":"SELECT id, title, message, level, source, status, alert_time, host_id FROM alerts WHERE status != 'resolved' AND deleted_at IS NULL ORDER BY alert_time DESC","operation":"select","description":"查询当前未解决的系统告警"}}

用户输入："查看严重告警"
返回：{"type":"database_operations","confidence":0.9,"parameters":{"sql":"SELECT id, title, message, level, source, status, alert_time, host_id FROM alerts WHERE level = 'critical' AND status != 'resolved' AND deleted_at IS NULL ORDER BY alert_time DESC","operation":"select","description":"查询严重级别告警"}}

用户输入："确认告警123"
返回：{"type":"database_operations","confidence":0.95,"parameters":{"sql":"UPDATE alerts SET status = 'acknowledged', acknowledged_time = CURRENT_TIMESTAMP WHERE id = 123","operation":"update","description":"确认指定告警","confirm_required":true,"target_info":"告警ID: 123"}}

## 🚨 最终检查清单：
1. 如果用户输入包含"密码"关键词，类型必须是database_operations，置信度≥0.95
2. 如果用户输入包含"主机"+"IP地址"+"密码"，类型必须是database_operations，置信度≥0.98
3. 如果用户输入是"修改/更新/改/设置 + 主机信息"，类型必须是database_operations
4. 必须返回标准JSON格式，包含type、confidence、parameters三个字段
5. confidence值必须是0.0-1.0之间的数字，不能是字符串

请严格按照以上规则识别用户意图，确保高准确率！`

	messages := []Message{
		{Role: "system", Content: systemPrompt},
		{Role: "user", Content: userInput},
	}

	r.logger.WithFields(logrus.Fields{
		"system_prompt_length": len(systemPrompt),
		"user_input":           userInput,
	}).Info("DeepSeek: Sending intent recognition request")

	response, err := r.deepseek.Chat(ctx, messages)
	if err != nil {
		r.logger.WithError(err).Error("DeepSeek: Intent recognition request failed")
		return nil, fmt.Errorf("failed to recognize intent: %w", err)
	}

	if len(response.Choices) == 0 {
		r.logger.Error("DeepSeek: No response choices returned")
		return nil, fmt.Errorf("no response from AI")
	}

	content := response.Choices[0].Message.Content
	r.logger.WithField("raw_response", content).Info("DeepSeek: Raw intent recognition response")

	// 提取JSON内容（处理代码块包裹的情况）
	jsonContent := r.extractJSONFromResponse(content)
	if jsonContent == "" {
		r.logger.WithFields(logrus.Fields{
			"raw_response": content,
		}).Warn("DeepSeek: No valid JSON found in response, using fallback")

		// 如果没有找到JSON，返回通用对话意图
		return &Intent{
			Type:       "general_chat",
			Confidence: 0.5,
			Parameters: map[string]interface{}{"original_input": userInput},
		}, nil
	}

	// 尝试解析JSON
	var intent Intent
	if err := json.Unmarshal([]byte(jsonContent), &intent); err != nil {
		r.logger.WithFields(logrus.Fields{
			"error":        err.Error(),
			"raw_response": content,
			"json_content": jsonContent,
		}).Warn("DeepSeek: Failed to parse intent JSON, using fallback")

		// 如果解析失败，返回通用对话意图
		return &Intent{
			Type:       "general_chat",
			Confidence: 0.5,
			Parameters: map[string]interface{}{"original_input": userInput},
		}, nil
	}

	r.logger.WithFields(logrus.Fields{
		"user_input": userInput,
		"intent":     intent.Type,
		"confidence": intent.Confidence,
		"parameters": intent.Parameters,
	}).Info("DeepSeek: Intent recognized successfully")

	return &intent, nil
}

// CommandGenerator 命令生成器
type CommandGenerator struct {
	deepseek *DeepSeekService
	logger   *logrus.Logger
}

// NewCommandGenerator 创建命令生成器
func NewCommandGenerator(deepseek *DeepSeekService, logger *logrus.Logger) *CommandGenerator {
	return &CommandGenerator{
		deepseek: deepseek,
		logger:   logger,
	}
}

// GenerateCommand 生成命令
func (g *CommandGenerator) GenerateCommand(ctx context.Context, intent *Intent, userInput string) (string, error) {
	// 根据意图类型选择不同的命令生成策略
	switch intent.Type {
	case "system_monitoring":
		return g.generateMonitoringCommand(ctx, intent, userInput)
	case "service_management":
		return g.generateServiceCommand(ctx, intent, userInput)
	case "log_analysis":
		return g.generateLogCommand(ctx, intent, userInput)
	case "file_operations":
		return g.generateFileCommand(ctx, intent, userInput)
	case "network_diagnostics":
		return g.generateNetworkCommand(ctx, intent, userInput)
	case "command_execution":
		return g.generateGenericCommand(ctx, intent, userInput)
	default:
		return g.generateGenericCommand(ctx, intent, userInput)
	}
}

// generateMonitoringCommand 生成监控命令
func (g *CommandGenerator) generateMonitoringCommand(ctx context.Context, intent *Intent, userInput string) (string, error) {
	systemPrompt := `你是一个Linux系统监控专家。根据用户需求生成安全的系统监控命令。

支持的监控类型：
- CPU使用率: top -bn1 | head -20
- 内存使用: free -h
- 磁盘使用: df -h
- 磁盘IO: iostat -x 1 1
- 网络流量: iftop -t -s 10 (如果可用) 或 cat /proc/net/dev
- 系统负载: uptime
- 进程监控: ps aux --sort=-%cpu | head -20
- 端口监听: netstat -tuln | grep LISTEN

请只返回命令，不要包含解释。确保命令安全且不会对系统造成影响。`

	prompt := fmt.Sprintf("用户需求: %s\n参数: %v", userInput, intent.Parameters)
	return g.executePrompt(ctx, systemPrompt, prompt)
}

// generateServiceCommand 生成服务管理命令
func (g *CommandGenerator) generateServiceCommand(ctx context.Context, intent *Intent, userInput string) (string, error) {
	systemPrompt := `你是一个Linux服务管理专家。根据用户需求生成安全的服务管理命令。

支持的服务操作：
- 查看服务状态: systemctl status <service>
- 启动服务: systemctl start <service>
- 停止服务: systemctl stop <service>
- 重启服务: systemctl restart <service>
- 重载配置: systemctl reload <service>
- 查看服务日志: journalctl -u <service> -n 50
- 列出所有服务: systemctl list-units --type=service

注意：只生成查看状态的命令，不要生成启动/停止/重启命令，这些需要用户确认。
请只返回命令，不要包含解释。`

	prompt := fmt.Sprintf("用户需求: %s\n参数: %v", userInput, intent.Parameters)
	return g.executePrompt(ctx, systemPrompt, prompt)
}

// generateLogCommand 生成日志查看命令
func (g *CommandGenerator) generateLogCommand(ctx context.Context, intent *Intent, userInput string) (string, error) {
	systemPrompt := `你是一个Linux日志分析专家。根据用户需求生成安全的日志查看命令。

常用日志命令：
- 系统日志: journalctl -n 50
- 内核日志: dmesg | tail -50
- 认证日志: tail -50 /var/log/auth.log
- 系统消息: tail -50 /var/log/syslog
- 错误日志: journalctl -p err -n 50
- 特定服务日志: journalctl -u <service> -n 50
- 实时日志: journalctl -f
- 按时间查看: journalctl --since "1 hour ago"

请只返回命令，不要包含解释。确保命令安全且只读取日志。`

	prompt := fmt.Sprintf("用户需求: %s\n参数: %v", userInput, intent.Parameters)
	return g.executePrompt(ctx, systemPrompt, prompt)
}

// generateFileCommand 生成文件操作命令
func (g *CommandGenerator) generateFileCommand(ctx context.Context, intent *Intent, userInput string) (string, error) {
	systemPrompt := `你是一个Linux文件系统专家。根据用户需求生成安全的文件查看命令。

安全的文件操作命令：
- 查看文件内容: cat <file> 或 less <file>
- 查看文件头部: head -20 <file>
- 查看文件尾部: tail -20 <file>
- 查看目录内容: ls -la <directory>
- 查找文件: find <path> -name "<pattern>" -type f
- 查看文件大小: du -sh <file/directory>
- 查看文件权限: stat <file>
- 查看磁盘使用: df -h

注意：只生成查看和读取命令，不要生成修改、删除或移动文件的命令。
请只返回命令，不要包含解释。`

	prompt := fmt.Sprintf("用户需求: %s\n参数: %v", userInput, intent.Parameters)
	return g.executePrompt(ctx, systemPrompt, prompt)
}

// generateNetworkCommand 生成网络诊断命令
func (g *CommandGenerator) generateNetworkCommand(ctx context.Context, intent *Intent, userInput string) (string, error) {
	systemPrompt := `你是一个网络诊断专家。根据用户需求生成安全的网络诊断命令。

常用网络诊断命令：
- 测试连通性: ping -c 4 <host>
- 测试端口: telnet <host> <port> 或 nc -zv <host> <port>
- 查看路由: traceroute <host>
- 查看网络接口: ip addr show 或 ifconfig
- 查看网络连接: netstat -tuln
- 查看监听端口: ss -tuln
- 查看ARP表: arp -a
- 查看DNS解析: nslookup <domain> 或 dig <domain>

请只返回命令，不要包含解释。确保命令安全且不会对网络造成影响。`

	prompt := fmt.Sprintf("用户需求: %s\n参数: %v", userInput, intent.Parameters)
	return g.executePrompt(ctx, systemPrompt, prompt)
}

// generateGenericCommand 生成通用命令
func (g *CommandGenerator) generateGenericCommand(ctx context.Context, intent *Intent, userInput string) (string, error) {
	systemPrompt := `你是一个Linux系统管理专家，根据用户的意图和输入生成相应的Linux命令。
请只返回命令本身，不要包含任何解释或额外文本。
确保命令是安全的，不会对系统造成损害。

安全命令示例：
- 查看CPU使用率: top -bn1 | head -20
- 查看内存使用: free -h
- 查看磁盘使用: df -h
- 查看进程: ps aux
- 查看网络连接: netstat -tuln
- 查看系统负载: uptime
- 查看日志: tail -50 /var/log/syslog

禁止生成的危险命令：
- 删除文件: rm, rmdir
- 格式化: mkfs, fdisk
- 系统控制: shutdown, reboot, halt
- 用户管理: userdel, passwd
- 权限修改: chmod 777, chown -R`

	prompt := fmt.Sprintf("意图类型: %s\n用户输入: %s\n参数: %v\n请生成对应的安全Linux命令:", intent.Type, userInput, intent.Parameters)
	return g.executePrompt(ctx, systemPrompt, prompt)
}

// executePrompt 执行提示并返回清理后的命令
func (g *CommandGenerator) executePrompt(ctx context.Context, systemPrompt, userPrompt string) (string, error) {
	messages := []Message{
		{Role: "system", Content: systemPrompt},
		{Role: "user", Content: userPrompt},
	}

	response, err := g.deepseek.Chat(ctx, messages)
	if err != nil {
		return "", fmt.Errorf("failed to generate command: %w", err)
	}

	if len(response.Choices) == 0 {
		return "", fmt.Errorf("no response from AI")
	}

	command := strings.TrimSpace(response.Choices[0].Message.Content)

	// 清理命令格式
	command = g.cleanCommand(command)

	// 安全检查
	if !g.isCommandSafe(command) {
		return "", fmt.Errorf("generated command is not safe: %s", command)
	}

	g.logger.WithFields(logrus.Fields{
		"command": command,
	}).Debug("Command generated and validated")

	return command, nil
}

// cleanCommand 清理命令格式
func (g *CommandGenerator) cleanCommand(command string) string {
	// 移除可能的代码块标记
	command = strings.TrimPrefix(command, "```bash")
	command = strings.TrimPrefix(command, "```sh")
	command = strings.TrimPrefix(command, "```")
	command = strings.TrimSuffix(command, "```")
	command = strings.TrimSpace(command)

	// 移除可能的解释文本
	lines := strings.Split(command, "\n")
	if len(lines) > 0 {
		command = strings.TrimSpace(lines[0])
	}

	return command
}

// isCommandSafe 检查命令安全性
func (g *CommandGenerator) isCommandSafe(command string) bool {
	// 危险命令列表
	dangerousCommands := []string{
		"rm ", "rmdir", "dd if=", "mkfs", "fdisk", "parted",
		"shutdown", "reboot", "halt", "poweroff", "init 0", "init 6",
		"passwd", "userdel", "groupdel", "useradd", "usermod",
		"iptables -F", "ufw --force", "ufw reset",
		"chmod 777", "chown -R", "chmod -R 777",
		"format", "del /f", "rmdir /s",
		"kill -9", "killall", "pkill",
		"mount", "umount", "fsck",
		"crontab -r", "crontab -e",
		"history -c", "history -w",
		"> /dev/", ">> /dev/",
	}

	commandLower := strings.ToLower(command)
	for _, dangerous := range dangerousCommands {
		if strings.Contains(commandLower, dangerous) {
			return false
		}
	}

	// 检查是否包含管道到危险位置
	if strings.Contains(commandLower, "> /") || strings.Contains(commandLower, ">> /") {
		return false
	}

	// 检查是否包含sudo（需要特殊处理）
	if strings.HasPrefix(commandLower, "sudo ") {
		return false
	}

	return true
}

// extractJSONFromResponse 从响应中提取JSON内容
// 处理DeepSeek返回的包裹在```json代码块中的JSON
func (r *IntentRecognizer) extractJSONFromResponse(content string) string {
	// 首先尝试查找```json代码块
	jsonBlockStart := strings.Index(content, "```json")
	if jsonBlockStart != -1 {
		// 找到```json，查找对应的结束标记
		jsonStart := jsonBlockStart + 7 // 跳过```json
		jsonBlockEnd := strings.Index(content[jsonStart:], "```")
		if jsonBlockEnd != -1 {
			jsonContent := strings.TrimSpace(content[jsonStart : jsonStart+jsonBlockEnd])
			return jsonContent
		}
	}

	// 如果没有找到```json代码块，尝试直接提取JSON
	jsonStart := strings.Index(content, "{")
	jsonEnd := strings.LastIndex(content, "}")

	if jsonStart == -1 || jsonEnd == -1 || jsonStart >= jsonEnd {
		return ""
	}

	jsonContent := content[jsonStart : jsonEnd+1]
	return jsonContent
}
