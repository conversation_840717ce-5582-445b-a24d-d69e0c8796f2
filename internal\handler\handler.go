package handler

import (
	"aiops-platform/internal/auth"
	"aiops-platform/internal/health"
	"aiops-platform/internal/model"
	"aiops-platform/internal/service"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// Handlers 处理器集合
type Handlers struct {
	Auth  *AuthHandler
	User  *UserHandler
	Host  *HostHandler
	Alert *AlertHandler
	Chat  *ChatHandler
	AI    *AIHandler // 新增AI处理器
	// Agent        *AgentHandler    // Agent处理器暂时移除
	Action       *ActionHandler   // 新增操作处理器
	Workflow     *WorkflowHandler // 新增工作流处理器
	Stats        *StatsHandler
	Operation    *OperationHandler
	Config       *ConfigHandler
	Frontend     *FrontendHandler
	Notification *NotificationHandler
	Health       *HealthHandler
	Session      *SessionHandler
	Model        *ModelHandler
}

// NewHandlers 创建处理器集合
func NewHandlers(services *service.Services, logger *logrus.Logger) *Handlers {
	return &Handlers{
		Auth:  NewAuthH<PERSON><PERSON>(services, logger),
		User:  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(services, logger),
		Host:  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(services, logger),
		Alert: NewAlertHandler(services, logger),
		Chat:  NewChatHandler(services, logger),
		AI:    NewAIHandler(services, logger), // 创建AI处理器
		// Agent:        NewAgentHandler(services.Agent),                                              // Agent处理器暂时移除
		Action:       NewActionHandler(logger, services.ActionExecutor, services.ActionRecognizer), // 创建操作处理器
		Workflow:     NewWorkflowHandler(services.Workflow, logger),                                // 创建工作流处理器
		Stats:        NewStatsHandler(services, logger),
		Operation:    NewOperationHandler(services, logger),
		Config:       NewConfigHandler(services, logger),
		Frontend:     NewFrontendHandler(services, logger),
		Notification: NewNotificationHandler(services, logger),
		Health:       NewHealthHandler(services, logger),
		Session:      NewSessionHandler(services, logger, services.EnhancedJWT),
		Model:        NewModelHandler(services.ModelManager, logger),
	}
}

// AuthHandler 认证处理器
type AuthHandler struct {
	services *service.Services
	logger   *logrus.Logger
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(services *service.Services, logger *logrus.Logger) *AuthHandler {
	return &AuthHandler{
		services: services,
		logger:   logger,
	}
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username   string `json:"username" binding:"required"`
	Password   string `json:"password" binding:"required"`
	RememberMe bool   `json:"remember_me"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
	TokenType    string `json:"token_type"`
	User         gin.H  `json:"user"`
}

// Login 用户登录
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	// 记录登录尝试
	h.logger.WithFields(logrus.Fields{
		"username":   req.Username,
		"client_ip":  c.ClientIP(),
		"user_agent": c.Request.UserAgent(),
	}).Info("Login attempt")

	// 简化的认证逻辑 - 仅用于开发测试
	if req.Username == "admin" && req.Password == "admin123" {
		// 模拟成功登录
		response := LoginResponse{
			AccessToken:  "mock-access-token-" + strconv.FormatInt(time.Now().Unix(), 10),
			RefreshToken: "mock-refresh-token-" + strconv.FormatInt(time.Now().Unix(), 10),
			ExpiresIn:    3600, // 1小时
			TokenType:    "Bearer",
			User: gin.H{
				"id":       1,
				"username": req.Username,
				"name":     "系统管理员",
				"role":     "admin",
				"email":    "<EMAIL>",
			},
		}

		h.logger.WithFields(logrus.Fields{
			"username": req.Username,
		}).Info("Login successful")

		c.JSON(http.StatusOK, gin.H{
			"code":    200,
			"message": "Login successful",
			"data":    response,
		})
		return
	}

	// 登录失败
	h.logger.WithFields(logrus.Fields{
		"username": req.Username,
	}).Warn("Login failed - invalid credentials")

	c.JSON(http.StatusUnauthorized, gin.H{
		"code":    401,
		"message": "Invalid username or password",
	})
}

// Logout 用户登出
func (h *AuthHandler) Logout(c *gin.Context) {
	h.logger.Info("User logout")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Logout successful",
	})
}

// GetProfile 获取用户信息
func (h *AuthHandler) GetProfile(c *gin.Context) {
	// 模拟用户信息
	user := gin.H{
		"id":         1,
		"username":   "admin",
		"name":       "系统管理员",
		"role":       "admin",
		"email":      "<EMAIL>",
		"avatar":     "",
		"department": "IT部门",
		"phone":      "13800138000",
		"created_at": "2025-01-01T00:00:00Z",
		"last_login": time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    user,
	})
}

// GetJWTManager 获取JWT管理器（用于中间件）
func (h *AuthHandler) GetJWTManager() *auth.JWTManager {
	if h.services.EnhancedJWT != nil {
		return h.services.EnhancedJWT.JWTManager
	}
	return nil
}

// UserHandler 用户处理器
type UserHandler struct {
	services *service.Services
	logger   *logrus.Logger
}

// NewUserHandler 创建用户处理器
func NewUserHandler(services *service.Services, logger *logrus.Logger) *UserHandler {
	return &UserHandler{
		services: services,
		logger:   logger,
	}
}

// HostHandler 主机处理器
type HostHandler struct {
	services *service.Services
	logger   *logrus.Logger
}

// NewHostHandler 创建主机处理器
func NewHostHandler(services *service.Services, logger *logrus.Logger) *HostHandler {
	return &HostHandler{
		services: services,
		logger:   logger,
	}
}

// ListHosts 获取主机列表
func (h *HostHandler) ListHosts(c *gin.Context) {
	var req model.HostListQuery
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid query parameters: " + err.Error(),
		})
		return
	}

	hosts, err := h.services.Host.ListHosts(&req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list hosts")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to list hosts",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    hosts,
	})
}

// CreateHost 创建主机
func (h *HostHandler) CreateHost(c *gin.Context) {
	var req model.HostCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Warn("Invalid request body for create host")
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求数据格式不正确，请检查输入的字段和数据类型",
			"details": err.Error(),
		})
		return
	}

	// TODO: 从认证中获取用户ID，暂时设为1
	req.CreatedBy = 1

	host, err := h.services.Host.CreateHost(&req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create host")

		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if err.Error() == "至少需要提供密码或SSH密钥路径中的一种认证方式" ||
			err.Error() == "主机名已存在，请使用不同的主机名" ||
			err.Error() == "IP地址已存在，请使用不同的IP地址" {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, gin.H{
			"code":    statusCode,
			"message": err.Error(),
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"host_id":    host.ID,
		"host_name":  host.Name,
		"ip_address": host.IPAddress,
	}).Info("Host created successfully")

	c.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "主机创建成功",
		"data":    host,
	})
}

// GetHost 获取主机详情
func (h *HostHandler) GetHost(c *gin.Context) {
	id, err := parseIDParam(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid host ID",
		})
		return
	}

	host, err := h.services.Host.GetHostByID(id)
	if err != nil {
		if err.Error() == "host not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Host not found",
			})
			return
		}
		h.logger.WithError(err).Error("Failed to get host")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get host",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    host,
	})
}

// UpdateHost 更新主机
func (h *HostHandler) UpdateHost(c *gin.Context) {
	id, err := parseIDParam(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid host ID",
		})
		return
	}

	var req model.HostUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request body: " + err.Error(),
		})
		return
	}

	host, err := h.services.Host.UpdateHost(id, &req)
	if err != nil {
		if err.Error() == "host not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Host not found",
			})
			return
		}
		h.logger.WithError(err).Error("Failed to update host")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to update host: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Host updated successfully",
		"data":    host,
	})
}

// DeleteHost 删除主机
func (h *HostHandler) DeleteHost(c *gin.Context) {
	id, err := parseIDParam(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid host ID",
		})
		return
	}

	err = h.services.Host.DeleteHost(id)
	if err != nil {
		if err.Error() == "host not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Host not found",
			})
			return
		}
		h.logger.WithError(err).Error("Failed to delete host")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to delete host: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Host deleted successfully",
	})
}

// TestConnection 测试主机连接
func (h *HostHandler) TestConnection(c *gin.Context) {
	id, err := parseIDParam(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid host ID",
		})
		return
	}

	result, err := h.services.Host.TestConnection(id)
	if err != nil {
		if err.Error() == "host not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Host not found",
			})
			return
		}
		h.logger.WithError(err).Error("Failed to test connection")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to test connection",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    result,
	})
}

// ExecuteCommand 执行远程命令
func (h *HostHandler) ExecuteCommand(c *gin.Context) {
	id, err := parseIDParam(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid host ID",
		})
		return
	}

	var req model.CommandExecuteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request body: " + err.Error(),
		})
		return
	}

	result, err := h.services.Host.ExecuteCommand(id, &req)
	if err != nil {
		if err.Error() == "host not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Host not found",
			})
			return
		}
		h.logger.WithError(err).Error("Failed to execute command")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to execute command",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    result,
	})
}

// AlertHandler 告警处理器
type AlertHandler struct {
	services *service.Services
	logger   *logrus.Logger
}

// NewAlertHandler 创建告警处理器
func NewAlertHandler(services *service.Services, logger *logrus.Logger) *AlertHandler {
	return &AlertHandler{
		services: services,
		logger:   logger,
	}
}

// ListAlerts 获取告警列表
func (h *AlertHandler) ListAlerts(c *gin.Context) {
	var req model.AlertListQuery
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.WithError(err).Error("Invalid alert list query parameters")
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid query parameters",
			"error":   err.Error(),
		})
		return
	}

	result, err := h.services.Alert.ListAlerts(&req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list alerts")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to list alerts",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    result,
	})
}

// CreateAlert 创建告警
func (h *AlertHandler) CreateAlert(c *gin.Context) {
	var req model.AlertCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid alert create request")
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	alert, err := h.services.Alert.CreateAlert(&req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create alert")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create alert",
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"alert_id": alert.ID,
		"title":    alert.Title,
		"level":    alert.Level,
	}).Info("Alert created successfully")

	c.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "Alert created successfully",
		"data":    alert,
	})
}

// GetAlert 获取单个告警
func (h *AlertHandler) GetAlert(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid alert ID",
		})
		return
	}

	alert, err := h.services.Alert.GetAlertByID(id)
	if err != nil {
		if err.Error() == "alert not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Alert not found",
			})
			return
		}
		h.logger.WithError(err).Error("Failed to get alert")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get alert",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    alert,
	})
}

// UpdateAlert 更新告警
func (h *AlertHandler) UpdateAlert(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid alert ID",
		})
		return
	}

	var req model.AlertUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid alert update request")
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	alert, err := h.services.Alert.UpdateAlert(id, &req)
	if err != nil {
		if err.Error() == "alert not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Alert not found",
			})
			return
		}
		h.logger.WithError(err).Error("Failed to update alert")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to update alert",
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"alert_id": alert.ID,
		"title":    alert.Title,
	}).Info("Alert updated successfully")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Alert updated successfully",
		"data":    alert,
	})
}

// DeleteAlert 删除告警
func (h *AlertHandler) DeleteAlert(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid alert ID",
		})
		return
	}

	err = h.services.Alert.DeleteAlert(id)
	if err != nil {
		if err.Error() == "alert not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Alert not found",
			})
			return
		}
		h.logger.WithError(err).Error("Failed to delete alert")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to delete alert",
		})
		return
	}

	h.logger.WithField("alert_id", id).Info("Alert deleted successfully")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Alert deleted successfully",
	})
}

// AcknowledgeAlert 确认告警
func (h *AlertHandler) AcknowledgeAlert(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid alert ID",
		})
		return
	}

	var req model.AlertAcknowledgeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid acknowledge request")
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	// TODO: 从认证中获取用户ID，暂时使用固定值
	userID := int64(1)

	err = h.services.Alert.AcknowledgeAlert(id, userID, req.Comment)
	if err != nil {
		if err.Error() == "alert not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Alert not found",
			})
			return
		}
		h.logger.WithError(err).Error("Failed to acknowledge alert")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to acknowledge alert",
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"alert_id": id,
		"user_id":  userID,
	}).Info("Alert acknowledged successfully")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Alert acknowledged successfully",
	})
}

// ResolveAlert 解决告警
func (h *AlertHandler) ResolveAlert(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid alert ID",
		})
		return
	}

	var req struct {
		Resolution string `json:"resolution" binding:"omitempty,max=500"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid resolve request")
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	err = h.services.Alert.ResolveAlert(id, req.Resolution)
	if err != nil {
		if err.Error() == "alert not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Alert not found",
			})
			return
		}
		h.logger.WithError(err).Error("Failed to resolve alert")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to resolve alert",
		})
		return
	}

	h.logger.WithField("alert_id", id).Info("Alert resolved successfully")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Alert resolved successfully",
	})
}

// CloseAlert 关闭告警
func (h *AlertHandler) CloseAlert(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid alert ID",
		})
		return
	}

	err = h.services.Alert.CloseAlert(id)
	if err != nil {
		if err.Error() == "alert not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Alert not found",
			})
			return
		}
		h.logger.WithError(err).Error("Failed to close alert")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to close alert",
		})
		return
	}

	h.logger.WithField("alert_id", id).Info("Alert closed successfully")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Alert closed successfully",
	})
}

// GetAlertSummary 获取告警摘要
func (h *AlertHandler) GetAlertSummary(c *gin.Context) {
	summary, err := h.services.Alert.GetAlertSummary()
	if err != nil {
		h.logger.WithError(err).Error("Failed to get alert summary")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get alert summary",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    summary,
	})
}

// ChatHandler 对话处理器
type ChatHandler struct {
	services *service.Services
	logger   *logrus.Logger
}

// NewChatHandler 创建对话处理器
func NewChatHandler(services *service.Services, logger *logrus.Logger) *ChatHandler {
	return &ChatHandler{
		services: services,
		logger:   logger,
	}
}

// StatsHandler 统计处理器
type StatsHandler struct {
	services *service.Services
	logger   *logrus.Logger
}

// NewStatsHandler 创建统计处理器
func NewStatsHandler(services *service.Services, logger *logrus.Logger) *StatsHandler {
	return &StatsHandler{
		services: services,
		logger:   logger,
	}
}

// GetSystemOverview 获取系统概览统计
func (h *StatsHandler) GetSystemOverview(c *gin.Context) {
	// 获取基础统计数据
	var hostCount, alertCount, sessionCount, userCount int64

	// 查询主机数量
	if err := h.services.DB.Model(&model.Host{}).Count(&hostCount).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count hosts")
		hostCount = 0
	}

	// 查询告警数量（活跃的）
	if err := h.services.DB.Model(&model.Alert{}).Where("status = ?", "active").Count(&alertCount).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count alerts")
		alertCount = 0
	}

	// 查询会话数量
	if err := h.services.DB.Model(&model.ChatSession{}).Count(&sessionCount).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count sessions")
		sessionCount = 0
	}

	// 查询用户数量
	if err := h.services.DB.Model(&model.User{}).Count(&userCount).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count users")
		userCount = 0
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data": gin.H{
			"hosts":    hostCount,
			"alerts":   alertCount,
			"sessions": sessionCount,
			"users":    userCount,
		},
	})
}

// GetSystemHealth 获取系统健康状态
func (h *StatsHandler) GetSystemHealth(c *gin.Context) {
	// 检查数据库连接
	dbStatus := "healthy"
	if h.services.DB == nil {
		dbStatus = "error"
	} else {
		// 尝试执行一个简单的查询来测试连接
		var count int64
		if err := h.services.DB.Raw("SELECT 1").Count(&count).Error; err != nil {
			dbStatus = "error"
		}
	}

	// 检查服务状态
	serviceStatus := gin.H{
		"database": dbStatus,
		"api":      "healthy",
		"chat":     "healthy",
	}

	// 计算整体健康状态
	overallStatus := "healthy"
	if dbStatus != "healthy" {
		overallStatus = "degraded"
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data": gin.H{
			"status":    overallStatus,
			"services":  serviceStatus,
			"timestamp": time.Now().Unix(),
		},
	})
}

// GetSystemMetrics 获取系统性能指标
func (h *StatsHandler) GetSystemMetrics(c *gin.Context) {
	// 模拟系统性能数据
	metrics := gin.H{
		"cpu": gin.H{
			"usage":     15.6,
			"cores":     8,
			"frequency": 2.4,
		},
		"memory": gin.H{
			"used":      2048,
			"total":     8192,
			"usage":     25.0,
			"available": 6144,
		},
		"disk": gin.H{
			"used":  45.2,
			"total": 100.0,
			"usage": 45.2,
		},
		"network": gin.H{
			"rx_bytes": 1024000,
			"tx_bytes": 512000,
			"rx_rate":  "1.2 MB/s",
			"tx_rate":  "0.8 MB/s",
		},
		"uptime":    time.Now().Unix() - **********, // 模拟启动时间
		"timestamp": time.Now().Unix(),
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    metrics,
	})
}

// GetOperationStats 获取操作统计（用于图表）
func (h *StatsHandler) GetOperationStats(c *gin.Context) {
	timeRange := c.Query("time_range")

	// 根据时间范围生成模拟数据
	var operationTrend []gin.H

	switch timeRange {
	case "24h":
		// 生成24小时的数据
		now := time.Now()
		for i := 23; i >= 0; i-- {
			hour := now.Add(time.Duration(-i) * time.Hour)
			operationTrend = append(operationTrend, gin.H{
				"hour":  hour.Format("15:04"),
				"count": (i%3 + 1) * 2, // 模拟数据：2-6之间的值
			})
		}
	case "7d":
		// 生成7天的数据
		now := time.Now()
		for i := 6; i >= 0; i-- {
			day := now.AddDate(0, 0, -i)
			operationTrend = append(operationTrend, gin.H{
				"date":  day.Format("01-02"),
				"count": (i%5 + 1) * 10, // 模拟数据：10-50之间的值
			})
		}
	default:
		// 默认返回24小时数据
		now := time.Now()
		for i := 23; i >= 0; i-- {
			hour := now.Add(time.Duration(-i) * time.Hour)
			operationTrend = append(operationTrend, gin.H{
				"hour":  hour.Format("15:04"),
				"count": (i%3 + 1) * 2,
			})
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data": gin.H{
			"operation_trend": operationTrend,
			"summary": gin.H{
				"total_operations": 156,
				"success_rate":     95.2,
				"avg_duration":     "2.3s",
			},
		},
	})
}

// OperationHandler 操作日志处理器
type OperationHandler struct {
	services *service.Services
	logger   *logrus.Logger
}

// NewOperationHandler 创建操作日志处理器
func NewOperationHandler(services *service.Services, logger *logrus.Logger) *OperationHandler {
	return &OperationHandler{
		services: services,
		logger:   logger,
	}
}

// ConfigHandler 配置处理器
type ConfigHandler struct {
	services *service.Services
	logger   *logrus.Logger
}

// NewConfigHandler 创建配置处理器
func NewConfigHandler(services *service.Services, logger *logrus.Logger) *ConfigHandler {
	return &ConfigHandler{
		services: services,
		logger:   logger,
	}
}

// FrontendHandler 前端处理器
type FrontendHandler struct {
	services *service.Services
	logger   *logrus.Logger
}

// NewFrontendHandler 创建前端处理器
func NewFrontendHandler(services *service.Services, logger *logrus.Logger) *FrontendHandler {
	return &FrontendHandler{
		services: services,
		logger:   logger,
	}
}

// NotificationHandler 通知处理器
type NotificationHandler struct {
	services *service.Services
	logger   *logrus.Logger
}

// NewNotificationHandler 创建通知处理器
func NewNotificationHandler(services *service.Services, logger *logrus.Logger) *NotificationHandler {
	return &NotificationHandler{
		services: services,
		logger:   logger,
	}
}

// HealthHandler 健康检查处理器
type HealthHandler struct {
	services      *service.Services
	logger        *logrus.Logger
	healthChecker *health.HealthChecker
}

// NewHealthHandler 创建健康检查处理器
func NewHealthHandler(services *service.Services, logger *logrus.Logger) *HealthHandler {
	healthChecker := health.NewHealthChecker(services.DB, logger)
	return &HealthHandler{
		services:      services,
		logger:        logger,
		healthChecker: healthChecker,
	}
}

// HealthCheck 健康检查
func (h *HealthHandler) HealthCheck(c *gin.Context) {
	requestID := c.GetString("request_id")

	// 使用健康检查器执行检查
	ctx := c.Request.Context()
	healthResponse := h.healthChecker.CheckAll(ctx)

	// 使用新的响应格式
	successResponse := model.SuccessResponse(healthResponse, requestID)

	// 根据健康状态返回相应的HTTP状态码
	statusCode := http.StatusOK
	if healthResponse.Status != "healthy" {
		statusCode = http.StatusServiceUnavailable
	}

	c.JSON(statusCode, successResponse)
}

// Metrics 系统指标
func (h *HealthHandler) Metrics(c *gin.Context) {
	requestID := c.GetString("request_id")

	// 获取系统指标
	ctx := c.Request.Context()
	metrics, err := h.healthChecker.GetMetrics(ctx)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get system metrics")
		appErr := model.NewAppError(model.ErrCodeInternalError, "获取系统指标失败", err)
		c.Error(appErr)
		return
	}

	// 使用新的响应格式
	successResponse := model.SuccessResponse(metrics, requestID)
	c.JSON(http.StatusOK, successResponse)
}

// Ready 就绪检查
func (h *HealthHandler) Ready(c *gin.Context) {
	requestID := c.GetString("request_id")

	// 简单的就绪检查，主要检查关键依赖
	ctx := c.Request.Context()

	// 检查数据库连接
	var count int64
	if err := h.services.DB.WithContext(ctx).Raw("SELECT 1").Count(&count).Error; err != nil {
		h.logger.WithError(err).Error("Database readiness check failed")
		appErr := model.NewAppError(model.ErrCodeDatabaseError, "数据库未就绪", err)
		c.Error(appErr)
		return
	}

	response := map[string]interface{}{
		"status":    "ready",
		"timestamp": time.Now().Unix(),
	}

	successResponse := model.SuccessResponse(response, requestID)
	c.JSON(http.StatusOK, successResponse)
}

// Live 存活检查
func (h *HealthHandler) Live(c *gin.Context) {
	requestID := c.GetString("request_id")

	response := map[string]interface{}{
		"status":    "alive",
		"timestamp": time.Now().Unix(),
	}

	successResponse := model.SuccessResponse(response, requestID)
	c.JSON(http.StatusOK, successResponse)
}

// parseIDParam 解析路径参数中的ID
func parseIDParam(c *gin.Context) (int64, error) {
	idStr := c.Param("id")
	return strconv.ParseInt(idStr, 10, 64)
}
