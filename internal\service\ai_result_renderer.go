package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// AIResultRenderer AI结果渲染器
type AIResultRenderer struct {
	deepseekService *EnhancedDeepSeekService
	logger          *logrus.Logger
}

// NewAIResultRenderer 创建AI结果渲染器
func NewAIResultRenderer(deepseekService *EnhancedDeepSeekService, logger *logrus.Logger) *AIResultRenderer {
	return &AIResultRenderer{
		deepseekService: deepseekService,
		logger:          logger,
	}
}

// RenderRequest 渲染请求
type RenderRequest struct {
	UserInput       string                 `json:"user_input"`
	Operation       string                 `json:"operation"`
	ExecutionResult *UnifiedExecutionResult `json:"execution_result"`
	AnalysisResult  *AnalysisResult        `json:"analysis_result"`
	RenderStyle     string                 `json:"render_style"` // detailed, summary, technical
	Context         map[string]interface{} `json:"context"`
}

// RenderResult 渲染结果
type RenderResult struct {
	Success       bool                   `json:"success"`
	Content       string                 `json:"content"`       // 渲染后的Markdown内容
	ContentType   string                 `json:"content_type"`  // markdown, html, text
	RenderTime    time.Duration          `json:"render_time"`
	TokensUsed    int                    `json:"tokens_used"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// RenderExecutionResult 渲染执行结果
func (arr *AIResultRenderer) RenderExecutionResult(ctx context.Context, req *RenderRequest) (*RenderResult, error) {
	start := time.Now()
	
	arr.logger.WithFields(logrus.Fields{
		"user_input":    req.UserInput,
		"operation":     req.Operation,
		"render_style":  req.RenderStyle,
		"has_analysis":  req.AnalysisResult != nil,
	}).Info("AIResultRenderer: 开始渲染执行结果")
	
	// 如果没有分析结果，使用基础渲染
	if req.AnalysisResult == nil {
		return arr.renderBasicResult(req, start), nil
	}
	
	// 构建渲染提示词
	prompt := arr.buildRenderPrompt(req)
	
	// 调用DeepSeek进行智能渲染
	deepseekReq := &DeepSeekRequest{
		Messages: []DeepSeekMessage{
			{
				Role:    "system",
				Content: arr.buildRenderSystemPrompt(req.RenderStyle),
			},
			{
				Role:    "user",
				Content: prompt,
			},
		},
		MaxTokens:   4000,
		Temperature: 0.4,
	}
	
	response, err := arr.deepseekService.CallWithComplexity(ctx, deepseekReq, ComplexityComplex)
	if err != nil {
		arr.logger.WithError(err).Error("AIResultRenderer: DeepSeek渲染失败，使用降级渲染")
		return arr.renderFallbackResult(req, start), nil
	}
	
	content := response.Choices[0].Message.Content
	
	// 清理和优化渲染内容
	content = arr.cleanAndOptimizeContent(content)
	
	result := &RenderResult{
		Success:     true,
		Content:     content,
		ContentType: "markdown",
		RenderTime:  time.Since(start),
		TokensUsed:  response.Usage.TotalTokens,
		Metadata: map[string]interface{}{
			"ai_model":     response.Model,
			"render_style": req.RenderStyle,
			"has_analysis": req.AnalysisResult != nil,
		},
	}
	
	arr.logger.WithFields(logrus.Fields{
		"render_time":   result.RenderTime,
		"tokens_used":   result.TokensUsed,
		"content_length": len(result.Content),
	}).Info("AIResultRenderer: 渲染完成")
	
	return result, nil
}

// buildRenderSystemPrompt 构建渲染系统提示词
func (arr *AIResultRenderer) buildRenderSystemPrompt(style string) string {
	basePrompt := `你是一个专业的AI运维助手和技术文档专家，负责将运维操作结果渲染成用户友好的格式。

核心要求：
1. 使用专业的Markdown格式
2. 内容结构清晰，层次分明
3. 适当使用emoji图标增强可读性
4. 语言风格专业但友好
5. 突出关键信息和重要发现
6. 提供可操作的建议

基础结构模板：
## 🎯 操作摘要
[简洁的操作描述]

## 📊 执行结果
[执行状态和关键数据]

## 🔍 深度分析
[AI分析的关键发现和洞察]

## 💡 专业建议
[基于分析的建议和后续步骤]

## ⚠️ 风险评估
[风险级别和注意事项]`

	switch style {
	case "detailed":
		return basePrompt + `

渲染风格：详细模式
- 包含完整的执行详情
- 展示所有数据表格
- 提供深入的技术分析
- 包含完整的建议列表`

	case "summary":
		return basePrompt + `

渲染风格：摘要模式
- 突出关键信息
- 简化数据展示
- 重点关注结论和建议
- 内容简洁明了`

	case "technical":
		return basePrompt + `

渲染风格：技术模式
- 包含技术细节和代码
- 展示原始数据
- 提供技术分析
- 面向技术人员`

	default:
		return basePrompt + `

渲染风格：标准模式
- 平衡详细程度和可读性
- 包含必要的技术信息
- 适合一般用户阅读`
	}
}

// buildRenderPrompt 构建渲染提示词
func (arr *AIResultRenderer) buildRenderPrompt(req *RenderRequest) string {
	var prompt strings.Builder
	
	prompt.WriteString(fmt.Sprintf("用户请求: %s\n", req.UserInput))
	prompt.WriteString(fmt.Sprintf("执行操作: %s\n", req.Operation))
	prompt.WriteString(fmt.Sprintf("操作类型: %s\n", req.ExecutionResult.ExecutionType))
	prompt.WriteString(fmt.Sprintf("执行状态: %v\n", req.ExecutionResult.Success))
	prompt.WriteString(fmt.Sprintf("执行时间: %v\n", req.ExecutionResult.ExecutionTime))
	
	// 添加执行结果内容
	if req.ExecutionResult.Content != "" {
		prompt.WriteString(fmt.Sprintf("执行结果内容:\n%s\n", req.ExecutionResult.Content))
	}
	
	// 添加AI分析结果
	if req.AnalysisResult != nil {
		prompt.WriteString(fmt.Sprintf("AI分析摘要: %s\n", req.AnalysisResult.Summary))
		
		if len(req.AnalysisResult.KeyFindings) > 0 {
			prompt.WriteString("关键发现:\n")
			for _, finding := range req.AnalysisResult.KeyFindings {
				prompt.WriteString(fmt.Sprintf("- %s\n", finding))
			}
		}
		
		if len(req.AnalysisResult.Insights) > 0 {
			prompt.WriteString("深度洞察:\n")
			for _, insight := range req.AnalysisResult.Insights {
				prompt.WriteString(fmt.Sprintf("- %s\n", insight))
			}
		}
		
		if len(req.AnalysisResult.Recommendations) > 0 {
			prompt.WriteString("专业建议:\n")
			for _, rec := range req.AnalysisResult.Recommendations {
				prompt.WriteString(fmt.Sprintf("- %s\n", rec))
			}
		}
		
		if req.AnalysisResult.RiskAssessment != "" {
			prompt.WriteString(fmt.Sprintf("风险评估: %s\n", req.AnalysisResult.RiskAssessment))
		}
		
		if len(req.AnalysisResult.NextSteps) > 0 {
			prompt.WriteString("后续步骤:\n")
			for _, step := range req.AnalysisResult.NextSteps {
				prompt.WriteString(fmt.Sprintf("- %s\n", step))
			}
		}
	}
	
	prompt.WriteString("\n请根据以上信息生成专业、友好的运维操作结果报告。")
	
	return prompt.String()
}

// renderBasicResult 渲染基础结果（无AI分析）
func (arr *AIResultRenderer) renderBasicResult(req *RenderRequest, start time.Time) *RenderResult {
	var content strings.Builder
	
	content.WriteString(fmt.Sprintf("## 🎯 操作摘要\n\n"))
	content.WriteString(fmt.Sprintf("**操作**: %s\n", req.Operation))
	content.WriteString(fmt.Sprintf("**状态**: %s\n", arr.getStatusEmoji(req.ExecutionResult.Success)))
	content.WriteString(fmt.Sprintf("**执行时间**: %v\n\n", req.ExecutionResult.ExecutionTime))
	
	content.WriteString("## 📊 执行结果\n\n")
	if req.ExecutionResult.Content != "" {
		content.WriteString(req.ExecutionResult.Content)
	} else {
		content.WriteString("操作已完成")
	}
	
	if req.ExecutionResult.Error != "" {
		content.WriteString(fmt.Sprintf("\n\n## ❌ 错误信息\n\n```\n%s\n```\n", req.ExecutionResult.Error))
	}
	
	return &RenderResult{
		Success:     true,
		Content:     content.String(),
		ContentType: "markdown",
		RenderTime:  time.Since(start),
		TokensUsed:  0,
		Metadata: map[string]interface{}{
			"render_type": "basic",
			"has_analysis": false,
		},
	}
}

// renderFallbackResult 渲染降级结果
func (arr *AIResultRenderer) renderFallbackResult(req *RenderRequest, start time.Time) *RenderResult {
	var content strings.Builder
	
	content.WriteString("## 🎯 操作摘要\n\n")
	content.WriteString(fmt.Sprintf("**操作**: %s\n", req.Operation))
	content.WriteString(fmt.Sprintf("**状态**: %s\n", arr.getStatusEmoji(req.ExecutionResult.Success)))
	content.WriteString(fmt.Sprintf("**执行时间**: %v\n\n", req.ExecutionResult.ExecutionTime))
	
	content.WriteString("## 📊 执行结果\n\n")
	if req.ExecutionResult.Content != "" {
		content.WriteString(req.ExecutionResult.Content)
	}
	
	// 如果有分析结果，添加基础分析信息
	if req.AnalysisResult != nil {
		content.WriteString("\n\n## 🔍 分析结果\n\n")
		content.WriteString(fmt.Sprintf("**摘要**: %s\n\n", req.AnalysisResult.Summary))
		
		if len(req.AnalysisResult.KeyFindings) > 0 {
			content.WriteString("**关键发现**:\n")
			for _, finding := range req.AnalysisResult.KeyFindings {
				content.WriteString(fmt.Sprintf("- %s\n", finding))
			}
			content.WriteString("\n")
		}
		
		if len(req.AnalysisResult.Recommendations) > 0 {
			content.WriteString("**建议**:\n")
			for _, rec := range req.AnalysisResult.Recommendations {
				content.WriteString(fmt.Sprintf("- %s\n", rec))
			}
		}
	}
	
	if req.ExecutionResult.Error != "" {
		content.WriteString(fmt.Sprintf("\n\n## ❌ 错误信息\n\n```\n%s\n```\n", req.ExecutionResult.Error))
	}
	
	return &RenderResult{
		Success:     true,
		Content:     content.String(),
		ContentType: "markdown",
		RenderTime:  time.Since(start),
		TokensUsed:  0,
		Metadata: map[string]interface{}{
			"render_type": "fallback",
			"has_analysis": req.AnalysisResult != nil,
		},
	}
}

// cleanAndOptimizeContent 清理和优化内容
func (arr *AIResultRenderer) cleanAndOptimizeContent(content string) string {
	// 移除多余的空行
	lines := strings.Split(content, "\n")
	var cleanedLines []string
	var lastLineEmpty bool
	
	for _, line := range lines {
		trimmed := strings.TrimSpace(line)
		isEmpty := trimmed == ""
		
		if isEmpty && lastLineEmpty {
			continue // 跳过连续的空行
		}
		
		cleanedLines = append(cleanedLines, line)
		lastLineEmpty = isEmpty
	}
	
	return strings.Join(cleanedLines, "\n")
}

// getStatusEmoji 获取状态emoji
func (arr *AIResultRenderer) getStatusEmoji(success bool) string {
	if success {
		return "✅ 成功"
	}
	return "❌ 失败"
}
