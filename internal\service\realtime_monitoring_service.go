package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// RealtimeMonitoringService 实时监控服务
type RealtimeMonitoringService struct {
	db           *gorm.DB
	logger       *logrus.Logger
	hostService  HostService
	alertService AlertService
	wsManager    *WebSocketManager
	config       *RealtimeMonitoringConfig
	running      bool
	stopChan     chan struct{}
	mutex        sync.RWMutex
	hostStatuses map[int64]*HostStatusInfo
}

// RealtimeMonitoringConfig 实时监控配置
type RealtimeMonitoringConfig struct {
	Enabled               bool          `json:"enabled"`
	CheckInterval         time.Duration `json:"check_interval"`
	StatusSyncInterval    time.Duration `json:"status_sync_interval"`
	AlertOnStatusChange   bool          `json:"alert_on_status_change"`
	MaxConcurrentChecks   int           `json:"max_concurrent_checks"`
	ConnectionTimeout     time.Duration `json:"connection_timeout"`
	EnableRealTimeUpdates bool          `json:"enable_real_time_updates"`
}

// HostStatusInfo 主机状态信息
type HostStatusInfo struct {
	HostID        int64     `json:"host_id"`
	HostName      string    `json:"host_name"`
	IPAddress     string    `json:"ip_address"`
	CurrentStatus string    `json:"current_status"`
	LastStatus    string    `json:"last_status"`
	LastCheck     time.Time `json:"last_check"`
	LastOnline    time.Time `json:"last_online"`
	CheckCount    int64     `json:"check_count"`
	FailureCount  int64     `json:"failure_count"`
	mutex         sync.RWMutex
}

// NewRealtimeMonitoringService 创建实时监控服务
func NewRealtimeMonitoringService(db *gorm.DB, logger *logrus.Logger, hostService HostService, alertService AlertService, wsManager *WebSocketManager) *RealtimeMonitoringService {
	config := &RealtimeMonitoringConfig{
		Enabled:               true,
		CheckInterval:         30 * time.Second, // 30秒检查一次
		StatusSyncInterval:    5 * time.Second,  // 5秒同步一次状态
		AlertOnStatusChange:   true,
		MaxConcurrentChecks:   10,
		ConnectionTimeout:     10 * time.Second,
		EnableRealTimeUpdates: true,
	}

	return &RealtimeMonitoringService{
		db:           db,
		logger:       logger,
		hostService:  hostService,
		alertService: alertService,
		wsManager:    wsManager,
		config:       config,
		stopChan:     make(chan struct{}),
		hostStatuses: make(map[int64]*HostStatusInfo),
	}
}

// Start 启动实时监控服务
func (rms *RealtimeMonitoringService) Start(ctx context.Context) error {
	rms.mutex.Lock()
	defer rms.mutex.Unlock()

	if rms.running {
		return fmt.Errorf("realtime monitoring service is already running")
	}

	if !rms.config.Enabled {
		rms.logger.Info("Realtime monitoring service is disabled")
		return nil
	}

	rms.running = true

	// 初始化主机状态
	if err := rms.initializeHostStatuses(); err != nil {
		rms.logger.WithError(err).Error("Failed to initialize host statuses")
		return err
	}

	// 启动监控循环
	go rms.runMonitoringLoop(ctx)

	// 启动状态同步循环
	go rms.runStatusSyncLoop(ctx)

	rms.logger.WithFields(logrus.Fields{
		"check_interval":       rms.config.CheckInterval,
		"status_sync_interval": rms.config.StatusSyncInterval,
		"hosts_count":          len(rms.hostStatuses),
	}).Info("🚀 Realtime monitoring service started")

	return nil
}

// Stop 停止实时监控服务
func (rms *RealtimeMonitoringService) Stop() error {
	rms.mutex.Lock()
	defer rms.mutex.Unlock()

	if !rms.running {
		return nil
	}

	close(rms.stopChan)
	rms.running = false

	rms.logger.Info("🛑 Realtime monitoring service stopped")
	return nil
}

// initializeHostStatuses 初始化主机状态
func (rms *RealtimeMonitoringService) initializeHostStatuses() error {
	var hosts []model.Host
	if err := rms.db.Where("deleted_at IS NULL").Find(&hosts).Error; err != nil {
		return fmt.Errorf("failed to load hosts: %w", err)
	}

	for _, host := range hosts {
		statusInfo := &HostStatusInfo{
			HostID:        host.ID,
			HostName:      host.Name,
			IPAddress:     host.IPAddress,
			CurrentStatus: host.Status,
			LastStatus:    host.Status,
			LastCheck:     time.Now(),
			CheckCount:    0,
			FailureCount:  0,
		}

		if host.LastConnected != nil {
			statusInfo.LastOnline = *host.LastConnected
		}

		rms.hostStatuses[host.ID] = statusInfo
	}

	rms.logger.WithField("hosts_count", len(rms.hostStatuses)).Info("Host statuses initialized")
	return nil
}

// runMonitoringLoop 运行监控循环
func (rms *RealtimeMonitoringService) runMonitoringLoop(ctx context.Context) {
	ticker := time.NewTicker(rms.config.CheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-rms.stopChan:
			return
		case <-ticker.C:
			rms.performHostChecks(ctx)
		}
	}
}

// runStatusSyncLoop 运行状态同步循环
func (rms *RealtimeMonitoringService) runStatusSyncLoop(ctx context.Context) {
	ticker := time.NewTicker(rms.config.StatusSyncInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-rms.stopChan:
			return
		case <-ticker.C:
			rms.syncHostStatuses(ctx)
		}
	}
}

// performHostChecks 执行主机检查
func (rms *RealtimeMonitoringService) performHostChecks(ctx context.Context) {
	rms.mutex.RLock()
	hostStatuses := make([]*HostStatusInfo, 0, len(rms.hostStatuses))
	for _, status := range rms.hostStatuses {
		hostStatuses = append(hostStatuses, status)
	}
	rms.mutex.RUnlock()

	// 使用协程池并发检查
	semaphore := make(chan struct{}, rms.config.MaxConcurrentChecks)
	var wg sync.WaitGroup

	for _, statusInfo := range hostStatuses {
		wg.Add(1)
		go func(si *HostStatusInfo) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			rms.checkSingleHost(ctx, si)
		}(statusInfo)
	}

	wg.Wait()
}

// checkSingleHost 检查单个主机
func (rms *RealtimeMonitoringService) checkSingleHost(ctx context.Context, statusInfo *HostStatusInfo) {
	statusInfo.mutex.Lock()
	defer statusInfo.mutex.Unlock()

	statusInfo.CheckCount++
	statusInfo.LastCheck = time.Now()

	// 使用hostService测试连接
	result, err := rms.hostService.TestConnection(statusInfo.HostID)

	newStatus := "offline"
	if err == nil && result.Success {
		newStatus = "online"
		statusInfo.LastOnline = time.Now()
		statusInfo.FailureCount = 0
	} else {
		statusInfo.FailureCount++
	}

	// 检查状态是否发生变化
	if statusInfo.CurrentStatus != newStatus {
		rms.logger.WithFields(logrus.Fields{
			"host_id":    statusInfo.HostID,
			"host_name":  statusInfo.HostName,
			"old_status": statusInfo.CurrentStatus,
			"new_status": newStatus,
		}).Info("🔄 Host status changed")

		statusInfo.LastStatus = statusInfo.CurrentStatus
		statusInfo.CurrentStatus = newStatus

		// 触发状态变更处理
		rms.handleStatusChange(statusInfo, newStatus)
	}
}

// handleStatusChange 处理状态变更
func (rms *RealtimeMonitoringService) handleStatusChange(statusInfo *HostStatusInfo, newStatus string) {
	// 更新数据库
	rms.updateDatabaseStatus(statusInfo.HostID, newStatus)

	// 发送实时更新
	if rms.config.EnableRealTimeUpdates {
		rms.sendRealtimeUpdate(statusInfo)
	}

	// 生成告警
	if rms.config.AlertOnStatusChange {
		rms.generateStatusChangeAlert(statusInfo, newStatus)
	}
}

// updateDatabaseStatus 更新数据库状态
func (rms *RealtimeMonitoringService) updateDatabaseStatus(hostID int64, status string) {
	err := rms.db.Model(&model.Host{}).Where("id = ?", hostID).Updates(map[string]interface{}{
		"status":         status,
		"last_connected": time.Now(),
	}).Error

	if err != nil {
		rms.logger.WithError(err).WithField("host_id", hostID).Error("Failed to update host status in database")
	}
}

// sendRealtimeUpdate 发送实时更新
func (rms *RealtimeMonitoringService) sendRealtimeUpdate(statusInfo *HostStatusInfo) {
	if rms.wsManager == nil {
		return
	}

	updateData := map[string]interface{}{
		"host_id":       statusInfo.HostID,
		"host_name":     statusInfo.HostName,
		"ip_address":    statusInfo.IPAddress,
		"status":        statusInfo.CurrentStatus,
		"last_status":   statusInfo.LastStatus,
		"last_check":    statusInfo.LastCheck,
		"check_count":   statusInfo.CheckCount,
		"failure_count": statusInfo.FailureCount,
		"updated_by":    "realtime_monitoring",
	}

	message := &WSMessage{
		Type:      "host_status_realtime_update",
		Data:      updateData,
		Timestamp: time.Now(),
	}

	rms.wsManager.BroadcastToAll(message)
}

// generateStatusChangeAlert 生成状态变更告警
func (rms *RealtimeMonitoringService) generateStatusChangeAlert(statusInfo *HostStatusInfo, newStatus string) {
	if rms.alertService == nil {
		return
	}

	alertTitle := fmt.Sprintf("主机状态变更：%s (%s)", statusInfo.HostName, statusInfo.IPAddress)
	alertDescription := fmt.Sprintf("主机状态从 %s 变更为 %s", statusInfo.LastStatus, newStatus)

	severity := "info"
	if newStatus == "offline" {
		severity = "warning"
		alertDescription += fmt.Sprintf("。连续失败次数：%d", statusInfo.FailureCount)
	}

	alertReq := &model.AlertCreateRequest{
		Title:   alertTitle,
		Message: alertDescription,
		Level:   severity,
		Source:  "monitoring",
		HostID:  &statusInfo.HostID,
		Metadata: map[string]interface{}{
			"status_change":     true,
			"old_status":        statusInfo.LastStatus,
			"new_status":        newStatus,
			"failure_count":     statusInfo.FailureCount,
			"monitoring_source": "realtime_monitoring",
		},
	}

	_, err := rms.alertService.CreateAlert(alertReq)
	if err != nil {
		rms.logger.WithError(err).Error("Failed to create status change alert")
	}
}

// syncHostStatuses 同步主机状态
func (rms *RealtimeMonitoringService) syncHostStatuses(ctx context.Context) {
	// 从数据库重新加载主机列表，处理新增/删除的主机
	var hosts []model.Host
	if err := rms.db.Where("deleted_at IS NULL").Find(&hosts).Error; err != nil {
		rms.logger.WithError(err).Error("Failed to sync host statuses")
		return
	}

	rms.mutex.Lock()
	defer rms.mutex.Unlock()

	// 更新现有主机状态
	currentHosts := make(map[int64]bool)
	for _, host := range hosts {
		currentHosts[host.ID] = true

		if statusInfo, exists := rms.hostStatuses[host.ID]; exists {
			// 更新现有主机信息
			statusInfo.mutex.Lock()
			statusInfo.HostName = host.Name
			statusInfo.IPAddress = host.IPAddress
			statusInfo.mutex.Unlock()
		} else {
			// 添加新主机
			statusInfo := &HostStatusInfo{
				HostID:        host.ID,
				HostName:      host.Name,
				IPAddress:     host.IPAddress,
				CurrentStatus: host.Status,
				LastStatus:    host.Status,
				LastCheck:     time.Now(),
				CheckCount:    0,
				FailureCount:  0,
			}
			rms.hostStatuses[host.ID] = statusInfo
		}
	}

	// 移除已删除的主机
	for hostID := range rms.hostStatuses {
		if !currentHosts[hostID] {
			delete(rms.hostStatuses, hostID)
		}
	}
}

// GetHostStatusInfo 获取主机状态信息
func (rms *RealtimeMonitoringService) GetHostStatusInfo(hostID int64) (*HostStatusInfo, error) {
	rms.mutex.RLock()
	defer rms.mutex.RUnlock()

	statusInfo, exists := rms.hostStatuses[hostID]
	if !exists {
		return nil, fmt.Errorf("host status info not found for host ID: %d", hostID)
	}

	// 返回副本以避免并发问题
	statusInfo.mutex.RLock()
	defer statusInfo.mutex.RUnlock()

	return &HostStatusInfo{
		HostID:        statusInfo.HostID,
		HostName:      statusInfo.HostName,
		IPAddress:     statusInfo.IPAddress,
		CurrentStatus: statusInfo.CurrentStatus,
		LastStatus:    statusInfo.LastStatus,
		LastCheck:     statusInfo.LastCheck,
		LastOnline:    statusInfo.LastOnline,
		CheckCount:    statusInfo.CheckCount,
		FailureCount:  statusInfo.FailureCount,
	}, nil
}

// GetAllHostStatuses 获取所有主机状态
func (rms *RealtimeMonitoringService) GetAllHostStatuses() map[int64]*HostStatusInfo {
	rms.mutex.RLock()
	defer rms.mutex.RUnlock()

	result := make(map[int64]*HostStatusInfo)
	for hostID, statusInfo := range rms.hostStatuses {
		statusInfo.mutex.RLock()
		result[hostID] = &HostStatusInfo{
			HostID:        statusInfo.HostID,
			HostName:      statusInfo.HostName,
			IPAddress:     statusInfo.IPAddress,
			CurrentStatus: statusInfo.CurrentStatus,
			LastStatus:    statusInfo.LastStatus,
			LastCheck:     statusInfo.LastCheck,
			LastOnline:    statusInfo.LastOnline,
			CheckCount:    statusInfo.CheckCount,
			FailureCount:  statusInfo.FailureCount,
		}
		statusInfo.mutex.RUnlock()
	}

	return result
}
