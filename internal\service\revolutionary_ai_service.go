package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"aiops-platform/internal/config"
	"aiops-platform/internal/intent_engine"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// RevolutionaryAIService 革命性AI服务
// 集成下一代意图识别系统，提供商用级AI运维能力
type RevolutionaryAIService struct {
	// 核心组件
	intentAdapter   *intent_engine.RevolutionaryIntentAdapter
	deepseekService *DeepSeekService
	contextManager  *ContextManager

	// 数据库和配置
	db     *gorm.DB
	config *config.Config
	logger *logrus.Logger

	// 服务状态
	isRunning bool
	startTime time.Time

	// 兼容性支持
	legacyAIService AIService
}

// NewRevolutionaryAIService 创建革命性AI服务
func NewRevolutionaryAIService(
	db *gorm.DB,
	cfg *config.Config,
	logger *logrus.Logger,
	hostService HostService,
) *RevolutionaryAIService {
	// 创建DeepSeek服务
	deepseekService := NewDeepSeekService(&cfg.DeepSeek, logger)

	// 创建革命性意图识别适配器
	intentAdapter := intent_engine.NewRevolutionaryIntentAdapter(deepseekService, logger)

	// 创建上下文管理器
	contextManager := NewContextManager(db, logger)

	// 创建传统AI服务作为降级方案
	legacyAIService := NewAIService(db, cfg, logger, hostService)

	service := &RevolutionaryAIService{
		intentAdapter:   intentAdapter,
		deepseekService: deepseekService,
		contextManager:  contextManager,
		db:              db,
		config:          cfg,
		logger:          logger,
		legacyAIService: legacyAIService,
		startTime:       time.Now(),
		isRunning:       true,
	}

	logger.Info("Revolutionary AI Service initialized successfully")
	return service
}

// ProcessMessage 处理消息 - 实现AIService接口
func (ras *RevolutionaryAIService) ProcessMessage(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	start := time.Now()

	ras.logger.WithFields(logrus.Fields{
		"session_id":    req.SessionID,
		"user_id":       req.UserID,
		"message":       req.Message,
		"message_lower": strings.ToLower(req.Message),
	}).Info("🔥 Revolutionary AI Service: 开始处理消息 - 检查是否为主机查询")

	// 🚀 临时集成统一执行引擎 - 解决"列出主机"问题
	if strings.Contains(strings.ToLower(req.Message), "列出主机") ||
		strings.Contains(strings.ToLower(req.Message), "查询主机") ||
		strings.Contains(strings.ToLower(req.Message), "主机列表") {

		ras.logger.Info("Revolutionary AI Service: 🎯 检测到主机查询，使用统一执行引擎")

		// 创建主机服务
		hostService := NewHostService(ras.db, ras.config, ras.logger)

		// 创建增强AI服务实例
		enhancedService := NewEnhancedAIService(ras.db, ras.config, ras.logger, hostService)

		// 调用增强服务处理
		response, err := enhancedService.ProcessMessage(ctx, req)
		if err == nil {
			ras.logger.Info("Revolutionary AI Service: ✅ 统一执行引擎处理成功")
			return response, nil
		}

		ras.logger.WithError(err).Warn("Revolutionary AI Service: ⚠️ 统一执行引擎失败，继续原有流程")
	}

	// 构建革命性意图识别请求
	legacyReq := &intent_engine.LegacyIntentRequest{
		SessionID: req.SessionID,
		UserID:    req.UserID,
		Message:   req.Message,
	}

	// 使用革命性意图识别系统
	intentResp, err := ras.intentAdapter.ProcessLegacyIntent(ctx, legacyReq)
	if err != nil {
		ras.logger.WithError(err).Error("Revolutionary intent processing failed, falling back to legacy service")

		// 降级到传统AI服务
		return ras.legacyAIService.ProcessMessage(ctx, req)
	}

	// 构建响应
	response := &ProcessMessageResponse{
		Content:        ras.generateResponseContent(intentResp, req.Message),
		Intent:         intentResp.Type,
		Confidence:     intentResp.Confidence,
		TokenCount:     0, // 暂时设为0
		Timestamp:      time.Now(),
		ProcessingTime: time.Since(start),
	}

	// 如果意图识别成功，添加额外信息到参数中
	if intentResp.Success {
		if response.Parameters == nil {
			response.Parameters = make(map[string]interface{})
		}
		response.Parameters["revolutionary_system"] = true
		response.Parameters["intent_parameters"] = intentResp.Parameters
		response.Parameters["processing_engine"] = "next_gen_intent_recognition"
		response.Parameters["accuracy_rate"] = ras.intentAdapter.GetSystemMetrics().AccuracyRate
	}

	ras.logger.WithFields(logrus.Fields{
		"session_id":      req.SessionID,
		"intent":          response.Intent,
		"confidence":      response.Confidence,
		"processing_time": response.ProcessingTime.Milliseconds(),
		"revolutionary":   true,
	}).Info("Revolutionary AI Service: Message processed successfully")

	return response, nil
}

// generateResponseContent 生成响应内容
func (ras *RevolutionaryAIService) generateResponseContent(intentResp *intent_engine.LegacyIntentResponse, originalMessage string) string {
	if !intentResp.Success {
		return fmt.Sprintf("抱歉，我无法理解您的请求：%s。请尝试重新表达或寻求帮助。", originalMessage)
	}

	// 根据意图类型生成专业响应
	switch intentResp.Type {
	case "database_operations":
		return ras.generateDatabaseOperationResponse(intentResp, originalMessage)
	case "ssh_operations":
		return ras.generateSSHOperationResponse(intentResp, originalMessage)
	case "monitoring_operations":
		return ras.generateMonitoringOperationResponse(intentResp, originalMessage)
	case "general_chat":
		return ras.generateGeneralChatResponse(intentResp, originalMessage)
	default:
		return fmt.Sprintf("我理解您想要进行 %s 相关的操作，置信度：%.2f。请提供更多详细信息以便我为您提供精确的帮助。", intentResp.Type, intentResp.Confidence)
	}
}

// generateDatabaseOperationResponse 生成数据库操作响应
func (ras *RevolutionaryAIService) generateDatabaseOperationResponse(intentResp *intent_engine.LegacyIntentResponse, originalMessage string) string {
	confidence := intentResp.Confidence

	if confidence >= 0.9 {
		return fmt.Sprintf("🎯 **高精度识别** (置信度: %.1f%%)\n\n我准确理解您要进行数据库相关操作：\n\n📝 **您的请求**: %s\n\n🔧 **建议操作**:\n• 我将为您处理相关的数据库操作\n• 系统会自动验证操作安全性\n• 如需确认，我会在执行前询问您\n\n💡 **下一步**: 请确认是否继续执行该操作？", confidence*100, originalMessage)
	} else if confidence >= 0.7 {
		return fmt.Sprintf("✅ **意图识别成功** (置信度: %.1f%%)\n\n我理解您想要进行数据库相关操作。\n\n📝 **您的请求**: %s\n\n🤔 **需要确认**:\n为了确保操作准确性，请您详细说明具体要执行的操作类型。", confidence*100, originalMessage)
	} else {
		return fmt.Sprintf("🔍 **意图分析** (置信度: %.1f%%)\n\n我检测到您可能想要进行数据库相关操作，但需要更多信息来确保准确性。\n\n📝 **您的请求**: %s\n\n💭 **建议**:\n请提供更具体的操作描述，例如：\n• 添加主机信息\n• 查询主机状态\n• 更新配置信息", confidence*100, originalMessage)
	}
}

// generateSSHOperationResponse 生成SSH操作响应
func (ras *RevolutionaryAIService) generateSSHOperationResponse(intentResp *intent_engine.LegacyIntentResponse, originalMessage string) string {
	confidence := intentResp.Confidence

	return fmt.Sprintf("🔐 **SSH操作识别** (置信度: %.1f%%)\n\n我理解您想要进行SSH相关操作：\n\n📝 **您的请求**: %s\n\n⚡ **系统能力**:\n• 远程命令执行\n• 连接状态检查\n• 安全性验证\n\n🛡️ **安全提醒**: 所有SSH操作都会经过安全审计，请确保您有相应权限。", confidence*100, originalMessage)
}

// generateMonitoringOperationResponse 生成监控操作响应
func (ras *RevolutionaryAIService) generateMonitoringOperationResponse(intentResp *intent_engine.LegacyIntentResponse, originalMessage string) string {
	confidence := intentResp.Confidence

	return fmt.Sprintf("📊 **监控操作识别** (置信度: %.1f%%)\n\n我理解您想要进行系统监控相关操作：\n\n📝 **您的请求**: %s\n\n🔍 **监控能力**:\n• 实时性能监控\n• 系统状态检查\n• 告警管理\n• 趋势分析\n\n📈 **建议**: 我可以为您提供详细的监控数据和分析报告。", confidence*100, originalMessage)
}

// generateGeneralChatResponse 生成通用聊天响应
func (ras *RevolutionaryAIService) generateGeneralChatResponse(intentResp *intent_engine.LegacyIntentResponse, originalMessage string) string {
	confidence := intentResp.Confidence

	return fmt.Sprintf("💬 **智能对话** (置信度: %.1f%%)\n\n您好！我是您的AI运维助手。\n\n📝 **您说**: %s\n\n🤖 **我的能力**:\n• 主机管理和运维操作\n• 系统监控和性能分析\n• 故障诊断和问题解决\n• 安全审计和合规检查\n\n❓ **需要帮助?** 请告诉我您想要进行什么运维操作，我会为您提供专业的协助！", confidence*100, originalMessage)
}

// ProcessMessageWithTools 使用工具处理消息 - 实现AIService接口
func (ras *RevolutionaryAIService) ProcessMessageWithTools(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	// 先使用革命性系统进行意图识别
	response, err := ras.ProcessMessage(ctx, req)
	if err != nil {
		return ras.legacyAIService.ProcessMessageWithTools(ctx, req)
	}

	// 如果置信度足够高，可以直接执行工具调用
	if response.Confidence >= 0.9 {
		ras.logger.WithFields(logrus.Fields{
			"session_id": req.SessionID,
			"intent":     response.Intent,
			"confidence": response.Confidence,
		}).Info("High confidence intent detected, considering tool execution")

		// 这里可以添加自动工具执行逻辑
		// 暂时返回当前响应
	}

	return response, nil
}

// CreateContext 创建上下文 - 实现AIService接口
func (ras *RevolutionaryAIService) CreateContext(sessionID string, userID int64) error {
	// 简化实现，直接返回成功
	ras.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    userID,
	}).Debug("Creating context for revolutionary AI service")
	return nil
}

// GetContext 获取上下文 - 实现AIService接口
func (ras *RevolutionaryAIService) GetContext(sessionID string) (*ConversationContext, error) {
	sessionCtx, err := ras.contextManager.GetContext(sessionID)
	if err != nil {
		return nil, err
	}

	// 转换SessionContext到ConversationContext
	conversationCtx := &ConversationContext{
		SessionID:    sessionCtx.SessionID,
		UserID:       sessionCtx.UserID,
		Variables:    sessionCtx.Variables,
		Messages:     make([]ConversationMessage, len(sessionCtx.Messages)),
		LastActivity: sessionCtx.LastActivity,
	}

	// 转换消息格式
	for i, msg := range sessionCtx.Messages {
		conversationCtx.Messages[i] = ConversationMessage{
			Role:      msg.Role,
			Content:   msg.Content,
			Timestamp: msg.Timestamp,
			Metadata:  msg.Metadata,
		}
	}

	return conversationCtx, nil
}

// UpdateContext 更新上下文 - 实现AIService接口
func (ras *RevolutionaryAIService) UpdateContext(sessionID string, updates map[string]interface{}) error {
	// 简化实现
	ras.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"updates":    updates,
	}).Debug("Updating context for revolutionary AI service")
	return nil
}

// ClearContext 清除上下文 - 实现AIService接口
func (ras *RevolutionaryAIService) ClearContext(sessionID string) error {
	// 简化实现
	ras.logger.WithField("session_id", sessionID).Debug("Clearing context for revolutionary AI service")
	return nil
}

// ExtractIntent 提取意图 - 实现AIService接口
func (ras *RevolutionaryAIService) ExtractIntent(ctx context.Context, message string, context *ConversationContext) (*IntentResult, error) {
	// 使用革命性意图识别系统
	legacyReq := &intent_engine.LegacyIntentRequest{
		SessionID: context.SessionID,
		UserID:    context.UserID,
		Message:   message,
	}

	intentResp, err := ras.intentAdapter.ProcessLegacyIntent(ctx, legacyReq)
	if err != nil {
		return nil, err
	}

	return &IntentResult{
		Type:       intentResp.Type,
		Confidence: intentResp.Confidence,
		Parameters: intentResp.Parameters,
	}, nil
}

// GetAvailableTools 获取可用工具 - 实现AIService接口
func (ras *RevolutionaryAIService) GetAvailableTools(userID int64) ([]ToolDefinition, error) {
	return ras.legacyAIService.GetAvailableTools(userID)
}

// ExecuteTool 执行工具 - 实现AIService接口
func (ras *RevolutionaryAIService) ExecuteTool(ctx context.Context, toolCall *ToolCall, context *ConversationContext) (*ToolResult, error) {
	return ras.legacyAIService.ExecuteTool(ctx, toolCall, context)
}

// GenerateResponse 生成响应 - 实现AIService接口
func (ras *RevolutionaryAIService) GenerateResponse(ctx context.Context, req *GenerateResponseRequest) (*GenerateResponseResult, error) {
	return ras.legacyAIService.GenerateResponse(ctx, req)
}

// SummarizeConversation 总结对话 - 实现AIService接口
func (ras *RevolutionaryAIService) SummarizeConversation(ctx context.Context, sessionID string) (*ConversationSummary, error) {
	return ras.legacyAIService.SummarizeConversation(ctx, sessionID)
}

// ValidateCommand 验证命令 - 实现AIService接口
func (ras *RevolutionaryAIService) ValidateCommand(ctx context.Context, command string, context *ConversationContext) (*CommandValidation, error) {
	return ras.legacyAIService.ValidateCommand(ctx, command, context)
}

// ProcessMessageWithWorkflow 使用工作流处理消息 - 实现AIService接口
func (ras *RevolutionaryAIService) ProcessMessageWithWorkflow(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	return ras.legacyAIService.ProcessMessageWithWorkflow(ctx, req)
}

// AnalyzeWorkflowIntent 分析工作流意图 - 实现AIService接口
func (ras *RevolutionaryAIService) AnalyzeWorkflowIntent(ctx context.Context, message string, sessionID string) (*WorkflowIntentAnalysis, error) {
	return ras.legacyAIService.AnalyzeWorkflowIntent(ctx, message, sessionID)
}

// GenerateWorkflowGuidance 生成工作流指导 - 实现AIService接口
func (ras *RevolutionaryAIService) GenerateWorkflowGuidance(ctx context.Context, workflowState *WorkflowState) (*WorkflowGuidance, error) {
	return ras.legacyAIService.GenerateWorkflowGuidance(ctx, workflowState)
}

// GetSystemStatus 获取系统状态
func (ras *RevolutionaryAIService) GetSystemStatus() map[string]interface{} {
	return map[string]interface{}{
		"service_type":      "revolutionary_ai_service",
		"is_running":        ras.isRunning,
		"start_time":        ras.startTime,
		"uptime_seconds":    time.Since(ras.startTime).Seconds(),
		"intent_system":     ras.intentAdapter.GetHealthStatus(),
		"supported_intents": len(ras.intentAdapter.GetSupportedIntents()),
	}
}
