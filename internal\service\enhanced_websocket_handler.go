package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedWebSocketHandler 增强型WebSocket处理器
type EnhancedWebSocketHandler struct {
	db                    *gorm.DB
	logger                *logrus.Logger
	enhancedDeepSeek      *EnhancedDeepSeekService
	resultCollector       *ExecutionResultCollector
	resultAnalyzer        *AIResultAnalyzer
	resultRenderer        *AIResultRenderer
	executionEngine       *UnifiedExecutionEngine
	hostService           HostService
}

// NewEnhancedWebSocketHandler 创建增强型WebSocket处理器
func NewEnhancedWebSocketHandler(
	db *gorm.DB,
	logger *logrus.Logger,
	enhancedDeepSeek *EnhancedDeepSeekService,
	executionEngine *UnifiedExecutionEngine,
	hostService HostService,
) *EnhancedWebSocketHandler {
	resultCollector := NewExecutionResultCollector(db, logger)
	resultAnalyzer := NewAIResultAnalyzer(enhancedDeepSeek, logger)
	resultRenderer := NewAIResultRenderer(enhancedDeepSeek, logger)
	
	return &EnhancedWebSocketHandler{
		db:                    db,
		logger:                logger,
		enhancedDeepSeek:      enhancedDeepSeek,
		resultCollector:       resultCollector,
		resultAnalyzer:        resultAnalyzer,
		resultRenderer:        resultRenderer,
		executionEngine:       executionEngine,
		hostService:           hostService,
	}
}

// ProcessMessageRequest 处理消息请求
type ProcessMessageRequest struct {
	SessionID string `json:"session_id"`
	UserID    int64  `json:"user_id"`
	Message   string `json:"message"`
}

// ProcessMessageResponse 处理消息响应
type ProcessMessageResponse struct {
	Content        string                 `json:"content"`
	Intent         string                 `json:"intent"`
	Confidence     float64                `json:"confidence"`
	Parameters     map[string]interface{} `json:"parameters"`
	TokenCount     int                    `json:"token_count"`
	ProcessingTime time.Duration          `json:"processing_time"`
	Timestamp      time.Time              `json:"timestamp"`
}

// ProcessMessage 处理消息 - 完整的5步骤AI驱动运维流程
func (ewh *EnhancedWebSocketHandler) ProcessMessage(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	start := time.Now()
	
	ewh.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
	}).Info("🚀 EnhancedWebSocketHandler: 开始完整AI驱动运维流程")
	
	// 步骤1: DeepSeek意图识别
	ewh.logger.Info("📋 步骤1: DeepSeek意图识别")
	intentResponse, err := ewh.enhancedDeepSeek.RecognizeIntent(ctx, req.Message)
	if err != nil {
		ewh.logger.WithError(err).Error("步骤1失败: 意图识别失败")
		return ewh.createErrorResponse("意图识别失败", err, start), nil
	}
	
	// 解析意图结果
	intent, err := ewh.parseIntentResponse(intentResponse.Choices[0].Message.Content)
	if err != nil {
		ewh.logger.WithError(err).Error("步骤1失败: 意图解析失败")
		return ewh.createErrorResponse("意图解析失败", err, start), nil
	}
	
	ewh.logger.WithFields(logrus.Fields{
		"intent_type": intent["intent_type"],
		"confidence":  intent["confidence"],
	}).Info("✅ 步骤1完成: 意图识别成功")
	
	// 步骤2: DeepSeek生成执行命令
	ewh.logger.Info("🔧 步骤2: DeepSeek生成执行命令")
	commandResponse, err := ewh.enhancedDeepSeek.GenerateExecutionCommand(ctx, intent, req.Message)
	if err != nil {
		ewh.logger.WithError(err).Error("步骤2失败: 命令生成失败")
		return ewh.createErrorResponse("命令生成失败", err, start), nil
	}
	
	// 解析命令生成结果
	commandInfo, err := ewh.parseCommandResponse(commandResponse.Choices[0].Message.Content)
	if err != nil {
		ewh.logger.WithError(err).Error("步骤2失败: 命令解析失败")
		return ewh.createErrorResponse("命令解析失败", err, start), nil
	}
	
	ewh.logger.WithFields(logrus.Fields{
		"execution_type": commandInfo["execution_type"],
		"generated_code": commandInfo["generated_code"],
	}).Info("✅ 步骤2完成: 命令生成成功")
	
	// 步骤3: 后端执行命令
	ewh.logger.Info("⚡ 步骤3: 后端执行命令")
	executionResult, err := ewh.executeCommand(ctx, commandInfo, intent, req)
	if err != nil {
		ewh.logger.WithError(err).Error("步骤3失败: 命令执行失败")
		return ewh.createErrorResponse("命令执行失败", err, start), nil
	}
	
	ewh.logger.WithFields(logrus.Fields{
		"success":        executionResult.Success,
		"execution_time": executionResult.ExecutionTime,
	}).Info("✅ 步骤3完成: 命令执行成功")
	
	// 步骤4: 执行结果回传DeepSeek进行分析
	ewh.logger.Info("🔍 步骤4: AI结果分析")
	analysisResult, err := ewh.analyzeExecutionResult(ctx, executionResult, req)
	if err != nil {
		ewh.logger.WithError(err).Warn("步骤4警告: AI分析失败，继续使用基础结果")
		// 分析失败不影响整体流程，继续进行
	}
	
	if analysisResult != nil {
		ewh.logger.WithFields(logrus.Fields{
			"key_findings":    len(analysisResult.KeyFindings),
			"recommendations": len(analysisResult.Recommendations),
		}).Info("✅ 步骤4完成: AI分析成功")
	}
	
	// 步骤5: DeepSeek智能渲染结果
	ewh.logger.Info("🎨 步骤5: AI智能渲染")
	renderResult, err := ewh.renderFinalResult(ctx, executionResult, analysisResult, req)
	if err != nil {
		ewh.logger.WithError(err).Warn("步骤5警告: AI渲染失败，使用降级渲染")
		// 渲染失败使用基础内容
		renderResult = &RenderResult{
			Success: true,
			Content: executionResult.Content,
		}
	}
	
	ewh.logger.WithFields(logrus.Fields{
		"render_success":  renderResult.Success,
		"content_length":  len(renderResult.Content),
		"total_time":      time.Since(start),
	}).Info("✅ 步骤5完成: AI渲染成功")
	
	// 构建最终响应
	response := &ProcessMessageResponse{
		Content:        renderResult.Content,
		Intent:         fmt.Sprintf("%v", intent["intent_type"]),
		Confidence:     ewh.getFloatValue(intent["confidence"], 0.8),
		Parameters:     intent,
		TokenCount:     intentResponse.Usage.TotalTokens + commandResponse.Usage.TotalTokens + renderResult.TokensUsed,
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
	}
	
	ewh.logger.WithFields(logrus.Fields{
		"total_processing_time": response.ProcessingTime,
		"total_tokens":          response.TokenCount,
		"final_confidence":      response.Confidence,
	}).Info("🎉 完整AI驱动运维流程成功完成")
	
	return response, nil
}

// parseIntentResponse 解析意图响应
func (ewh *EnhancedWebSocketHandler) parseIntentResponse(content string) (map[string]interface{}, error) {
	jsonContent := ewh.extractJSONFromResponse(content)
	if jsonContent == "" {
		return nil, fmt.Errorf("未找到有效的JSON内容")
	}
	
	var intent map[string]interface{}
	if err := json.Unmarshal([]byte(jsonContent), &intent); err != nil {
		return nil, fmt.Errorf("解析意图JSON失败: %w", err)
	}
	
	return intent, nil
}

// parseCommandResponse 解析命令响应
func (ewh *EnhancedWebSocketHandler) parseCommandResponse(content string) (map[string]interface{}, error) {
	jsonContent := ewh.extractJSONFromResponse(content)
	if jsonContent == "" {
		return nil, fmt.Errorf("未找到有效的JSON内容")
	}
	
	var command map[string]interface{}
	if err := json.Unmarshal([]byte(jsonContent), &command); err != nil {
		return nil, fmt.Errorf("解析命令JSON失败: %w", err)
	}
	
	return command, nil
}

// executeCommand 执行命令
func (ewh *EnhancedWebSocketHandler) executeCommand(
	ctx context.Context,
	commandInfo map[string]interface{},
	intent map[string]interface{},
	req *ProcessMessageRequest,
) (*UnifiedExecutionResult, error) {
	// 构建执行请求
	execReq := &ExecutionRequest{
		SessionID:   req.SessionID,
		UserID:      req.UserID,
		OriginalMsg: req.Message,
		Intent: &Intent{
			Type:       fmt.Sprintf("%v", intent["intent_type"]),
			Confidence: ewh.getFloatValue(intent["confidence"], 0.8),
			Parameters: intent,
		},
		Context: map[string]interface{}{
			"command_info": commandInfo,
			"user_input":   req.Message,
		},
	}
	
	// 使用统一执行引擎执行
	return ewh.executionEngine.Execute(ctx, execReq)
}

// analyzeExecutionResult 分析执行结果
func (ewh *EnhancedWebSocketHandler) analyzeExecutionResult(
	ctx context.Context,
	executionResult *UnifiedExecutionResult,
	req *ProcessMessageRequest,
) (*AnalysisResult, error) {
	analysisReq := &AnalysisRequest{
		UserInput:       req.Message,
		Operation:       executionResult.Action,
		ExecutionResult: executionResult,
		Context: map[string]interface{}{
			"session_id": req.SessionID,
			"user_id":    req.UserID,
		},
	}
	
	return ewh.resultAnalyzer.AnalyzeExecutionResult(ctx, analysisReq)
}

// renderFinalResult 渲染最终结果
func (ewh *EnhancedWebSocketHandler) renderFinalResult(
	ctx context.Context,
	executionResult *UnifiedExecutionResult,
	analysisResult *AnalysisResult,
	req *ProcessMessageRequest,
) (*RenderResult, error) {
	renderReq := &RenderRequest{
		UserInput:       req.Message,
		Operation:       executionResult.Action,
		ExecutionResult: executionResult,
		AnalysisResult:  analysisResult,
		RenderStyle:     "detailed", // 可以根据用户偏好调整
		Context: map[string]interface{}{
			"session_id": req.SessionID,
			"user_id":    req.UserID,
		},
	}
	
	return ewh.resultRenderer.RenderExecutionResult(ctx, renderReq)
}

// createErrorResponse 创建错误响应
func (ewh *EnhancedWebSocketHandler) createErrorResponse(message string, err error, start time.Time) *ProcessMessageResponse {
	return &ProcessMessageResponse{
		Content:        fmt.Sprintf("❌ %s: %s\n\n💡 请尝试重新描述您的需求或联系技术支持", message, err.Error()),
		Intent:         "error",
		Confidence:     0.0,
		Parameters:     map[string]interface{}{"error": err.Error()},
		TokenCount:     0,
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
	}
}

// extractJSONFromResponse 从响应中提取JSON
func (ewh *EnhancedWebSocketHandler) extractJSONFromResponse(content string) string {
	// 实现与AIResultAnalyzer中相同的JSON提取逻辑
	return ewh.resultAnalyzer.extractJSONFromResponse(content)
}

// getFloatValue 安全获取float值
func (ewh *EnhancedWebSocketHandler) getFloatValue(value interface{}, defaultValue float64) float64 {
	if v, ok := value.(float64); ok {
		return v
	}
	if v, ok := value.(float32); ok {
		return float64(v)
	}
	return defaultValue
}
