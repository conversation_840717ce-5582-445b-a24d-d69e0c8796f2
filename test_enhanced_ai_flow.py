#!/usr/bin/env python3
"""
测试增强AI流程的简单脚本
"""

import json
import requests
import time
import websocket
import threading

# 服务器配置
SERVER_URL = "http://localhost:8080"
WS_URL = "ws://localhost:8080/ws"

def test_http_api():
    """测试HTTP API"""
    print("🧪 测试HTTP API...")
    
    # 测试健康检查
    try:
        response = requests.get(f"{SERVER_URL}/health")
        print(f"✅ 健康检查: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False
    
    # 测试聊天API
    test_messages = [
        "列出所有主机",
        "对**************做一个全面的巡检",
        "查询数据库中的用户信息",
        "你好，请介绍一下你的功能"
    ]
    
    for message in test_messages:
        try:
            print(f"\n📤 发送消息: {message}")
            
            payload = {
                "message": message,
                "session_id": f"test_session_{int(time.time())}",
                "user_id": 1
            }
            
            start_time = time.time()
            response = requests.post(
                f"{SERVER_URL}/api/v1/chat/message",
                json=payload,
                timeout=60
            )
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 响应成功 ({end_time - start_time:.2f}s)")
                print(f"   意图: {data.get('intent', 'N/A')}")
                print(f"   置信度: {data.get('confidence', 'N/A')}")
                print(f"   Token数: {data.get('token_count', 'N/A')}")
                print(f"   内容长度: {len(data.get('content', ''))}")
                if data.get('enhanced'):
                    print("   🚀 增强AI处理")
                else:
                    print("   🔄 传统处理")
            else:
                print(f"❌ 响应失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
        
        time.sleep(1)  # 避免请求过快
    
    return True

def test_websocket():
    """测试WebSocket连接"""
    print("\n🧪 测试WebSocket连接...")
    
    messages_received = []
    connection_established = threading.Event()
    
    def on_message(ws, message):
        try:
            data = json.loads(message)
            messages_received.append(data)
            print(f"📥 收到消息: {data.get('type', 'unknown')}")
            
            if data.get('type') == 'assistant_message':
                content = data.get('data', {}).get('content', '')
                print(f"   内容长度: {len(content)}")
                if data.get('data', {}).get('enhanced'):
                    print("   🚀 增强AI处理")
                    workflow_steps = data.get('data', {}).get('workflow_steps', [])
                    if workflow_steps:
                        print(f"   工作流步骤: {', '.join(workflow_steps)}")
                        
        except json.JSONDecodeError:
            print(f"📥 收到非JSON消息: {message}")
    
    def on_error(ws, error):
        print(f"❌ WebSocket错误: {error}")
    
    def on_close(ws, close_status_code, close_msg):
        print("🔌 WebSocket连接关闭")
    
    def on_open(ws):
        print("✅ WebSocket连接建立")
        connection_established.set()
        
        # 发送测试消息
        test_messages = [
            {"type": "message", "data": "列出所有主机"},
            {"type": "message", "data": "对**************做一个全面的巡检"}
        ]
        
        for msg in test_messages:
            print(f"📤 发送WebSocket消息: {msg['data']}")
            ws.send(json.dumps(msg))
            time.sleep(5)  # 等待响应
    
    try:
        ws = websocket.WebSocketApp(
            WS_URL,
            on_open=on_open,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close
        )
        
        # 在单独线程中运行WebSocket
        ws_thread = threading.Thread(target=ws.run_forever)
        ws_thread.daemon = True
        ws_thread.start()
        
        # 等待连接建立
        if connection_established.wait(timeout=10):
            print("✅ WebSocket连接测试成功")
            time.sleep(15)  # 等待消息处理
            ws.close()
            return True
        else:
            print("❌ WebSocket连接超时")
            return False
            
    except Exception as e:
        print(f"❌ WebSocket测试失败: {e}")
        return False

def test_system_status():
    """测试系统状态"""
    print("\n🧪 测试系统状态...")
    
    endpoints = [
        "/api/v1/system/status",
        "/api/v1/chat/health",
        "/api/v1/chat/statistics"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{SERVER_URL}{endpoint}")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {endpoint}: 正常")
                if 'enhanced_ai' in data:
                    print(f"   增强AI状态: {data['enhanced_ai']}")
                if 'health' in data:
                    health = data['health']
                    print(f"   健康状态: {len([k for k, v in health.items() if v])} / {len(health)} 组件正常")
            else:
                print(f"❌ {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试增强AI运维管理平台")
    print("=" * 50)
    
    # 测试HTTP API
    if not test_http_api():
        print("❌ HTTP API测试失败，跳过后续测试")
        return
    
    # 测试系统状态
    test_system_status()
    
    # 测试WebSocket
    test_websocket()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")

if __name__ == "__main__":
    main()
