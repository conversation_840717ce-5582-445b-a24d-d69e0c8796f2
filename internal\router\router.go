package router

import (
	"net/http"

	"aiops-platform/internal/config"
	"aiops-platform/internal/handler"
	"aiops-platform/internal/middleware"
	"aiops-platform/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// New 创建路由
func New(cfg *config.Config, services *service.Services, logger *logrus.Logger) *gin.Engine {
	// 创建Gin引擎
	r := gin.New()

	// 添加中间件
	r.Use(middleware.RequestIDMiddleware())
	r.Use(middleware.ErrorHandlerMiddleware(logger))
	r.Use(middleware.RequestLoggerMiddleware(logger))
	r.Use(middleware.SecurityHeadersMiddleware())
	r.Use(middleware.CORSMiddleware([]string{"*"})) // 生产环境应该配置具体的域名

	// 创建处理器
	handlers := handler.NewHandlers(services, logger)

	// 健康检查端点
	r.GET("/health", handlers.Health.HealthCheck) // 完整健康检查
	r.GET("/health/ready", handlers.Health.Ready) // 就绪检查
	r.GET("/health/live", handlers.Health.Live)   // 存活检查
	r.GET("/metrics", handlers.Health.Metrics)    // 系统指标

	// 兼容性API路由（简化版本，用于前端兼容）
	compatAPI := r.Group("/api")
	{
		// 简化的聊天接口，兼容前端调用
		compatAPI.POST("/chat", handlers.Chat.SendMessage)
	}

	// API路由组
	api := r.Group("/api/v1")
	{
		// 认证相关路由
		auth := api.Group("/auth")
		{
			auth.POST("/login", handlers.Auth.Login)
			auth.POST("/logout", handlers.Auth.Logout)
			auth.GET("/profile", handlers.Auth.GetProfile)
		}

		// 会话管理（需要认证）
		sessions := api.Group("/sessions")
		{
			sessions.GET("", handlers.Session.GetUserSessions)
			sessions.DELETE("/:sessionId", handlers.Session.RevokeSession)
			sessions.DELETE("", handlers.Session.RevokeAllSessions)
			sessions.POST("/refresh", handlers.Session.RefreshToken)
		}

		// 设备管理（需要认证）
		devices := api.Group("/devices")
		{
			devices.GET("", handlers.Session.GetUserDevices)
			devices.PUT("/:deviceId/trust", handlers.Session.TrustDevice)
			devices.DELETE("/:deviceId", handlers.Session.RevokeDevice)
		}

		// 对话管理（暂时无需认证）
		chat := api.Group("/chat")
		{
			chat.POST("/sessions", handlers.Chat.CreateSession)
			chat.GET("/sessions", handlers.Chat.ListSessions)
			chat.GET("/sessions/:id", handlers.Chat.GetSession)
			chat.DELETE("/sessions/:id", handlers.Chat.DeleteSession)
			chat.POST("/message", handlers.Chat.SendMessage)
			chat.GET("/sessions/:id/messages", handlers.Chat.GetMessages)
		}

		// 统计报表（基础功能，暂时无需认证）
		stats := api.Group("/stats")
		{
			stats.GET("/overview", handlers.Stats.GetSystemOverview)
			stats.GET("/health", handlers.Stats.GetSystemHealth)
			stats.GET("/metrics", handlers.Stats.GetSystemMetrics)
			stats.GET("/operations", handlers.Stats.GetOperationStats)
		}

		// 主机管理（基础功能，暂时无需认证）
		hosts := api.Group("/hosts")
		{
			hosts.GET("", handlers.Host.ListHosts)
			hosts.POST("", handlers.Host.CreateHost)
			hosts.GET("/:id", handlers.Host.GetHost)
			hosts.PUT("/:id", handlers.Host.UpdateHost)
			hosts.DELETE("/:id", handlers.Host.DeleteHost)
			hosts.POST("/:id/test", handlers.Host.TestConnection)
			hosts.POST("/:id/execute", handlers.Host.ExecuteCommand)
		}

		// 告警管理（基础功能，暂时无需认证）
		alerts := api.Group("/alerts")
		{
			alerts.GET("", handlers.Alert.ListAlerts)
			alerts.POST("", handlers.Alert.CreateAlert)
			alerts.GET("/:id", handlers.Alert.GetAlert)
			alerts.PUT("/:id", handlers.Alert.UpdateAlert)
			alerts.DELETE("/:id", handlers.Alert.DeleteAlert)
			alerts.POST("/:id/acknowledge", handlers.Alert.AcknowledgeAlert)
			alerts.POST("/:id/resolve", handlers.Alert.ResolveAlert)
			alerts.POST("/:id/close", handlers.Alert.CloseAlert)
			alerts.GET("/summary", handlers.Alert.GetAlertSummary)
		}

		// AI服务（基础功能，暂时无需认证）
		ai := api.Group("/ai")
		{
			ai.POST("/message", handlers.AI.ProcessMessage)                                // 处理消息（带工具）
			ai.POST("/message/basic", handlers.AI.ProcessBasicMessage)                     // 处理基础消息
			ai.GET("/context/:session_id", handlers.AI.GetContext)                         // 获取上下文
			ai.DELETE("/context/:session_id", handlers.AI.ClearContext)                    // 清除上下文
			ai.GET("/tools", handlers.AI.GetAvailableTools)                                // 获取可用工具
			ai.GET("/conversation/:session_id/summary", handlers.AI.SummarizeConversation) // 总结对话
			ai.POST("/validate-command", handlers.AI.ValidateCommand)                      // 验证命令
			ai.POST("/generate", handlers.AI.GenerateResponse)                             // 生成响应
		}

		// 模型管理（基础功能，暂时无需认证）
		models := api.Group("/models")
		{
			models.GET("/available", handlers.Model.GetAvailableModels)    // 获取可用模型列表
			models.GET("/current", handlers.Model.GetCurrentModel)         // 获取当前模型
			models.POST("/switch", handlers.Model.SwitchModel)             // 切换模型
			models.GET("/provider/:provider", handlers.Model.GetModelsByProvider) // 根据提供商获取模型
			models.GET("/:name", handlers.Model.GetModelInfo)              // 获取特定模型信息
		}

		// Agent服务暂时移除
		/*
			agent := api.Group("/agent")
			{
				agent.POST("/start", handlers.Agent.StartAgent)                  // 启动Agent
				agent.POST("/stop", handlers.Agent.StopAgent)                    // 停止Agent
				agent.GET("/status", handlers.Agent.GetAgentStatus)              // 获取Agent状态
				agent.POST("/execute", handlers.Agent.ExecuteCommand)            // 执行命令
				agent.POST("/execute/async", handlers.Agent.ExecuteCommandAsync) // 异步执行命令
				agent.GET("/executions/:id", handlers.Agent.GetExecutionResult)  // 获取执行结果
				agent.DELETE("/executions/:id", handlers.Agent.CancelExecution)  // 取消执行
				agent.GET("/executions", handlers.Agent.ListExecutions)          // 列出执行记录
				agent.POST("/validate", handlers.Agent.ValidateCommand)          // 验证命令
				agent.POST("/risk-level", handlers.Agent.GetCommandRiskLevel)    // 获取命令风险等级
				agent.GET("/resource-usage", handlers.Agent.GetResourceUsage)    // 获取资源使用情况
				agent.GET("/system-metrics", handlers.Agent.GetSystemMetrics)    // 获取系统指标
				agent.GET("/queue-status", handlers.Agent.GetQueueStatus)        // 获取队列状态
			}
		*/

		// 操作管理（基础功能，暂时无需认证）
		actions := api.Group("/actions")
		{
			actions.POST("/execute", handlers.Action.ExecuteAction)            // 执行操作
			actions.POST("/recognize", handlers.Action.RecognizeActions)       // 识别操作
			actions.GET("/history", handlers.Action.GetActionHistory)          // 获取操作历史
			actions.POST("/:action_id/confirm", handlers.Action.ConfirmAction) // 确认高风险操作
			actions.GET("/:action_id/status", handlers.Action.GetActionStatus) // 获取操作状态
		}

		// 工作流管理（基础功能，暂时无需认证）
		workflow := api.Group("/workflow")
		{
			workflow.POST("/trigger", handlers.Workflow.TriggerWorkflow)                // 触发工作流
			workflow.POST("/:instance_id/input", handlers.Workflow.ProcessInput)        // 处理用户输入
			workflow.GET("/active", handlers.Workflow.GetActiveWorkflows)               // 获取活跃工作流
			workflow.GET("/:instance_id", handlers.Workflow.GetWorkflowInstance)        // 获取工作流实例
			workflow.DELETE("/:instance_id", handlers.Workflow.CancelWorkflow)          // 取消工作流
			workflow.POST("/analyze-intent", handlers.Workflow.AnalyzeIntent)           // 分析用户意图
			workflow.GET("/guidance", handlers.Workflow.GenerateGuidance)               // 生成工作流引导
			workflow.GET("/definitions", handlers.Workflow.GetWorkflowDefinitions)      // 获取工作流定义
			workflow.GET("/metrics", handlers.Workflow.GetWorkflowMetrics)              // 获取工作流指标
			workflow.GET("/:instance_id/history", handlers.Workflow.GetWorkflowHistory) // 获取工作流历史
		}

		// 操作日志（临时实现）
		api.GET("/operations", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"code":    200,
				"message": "success",
				"data": []gin.H{
					{
						"id":          1,
						"type":        "system",
						"description": "系统启动",
						"timestamp":   "2025-07-22T13:43:00Z",
						"status":      "success",
					},
					{
						"id":          2,
						"type":        "api",
						"description": "API健康检查",
						"timestamp":   "2025-07-22T13:42:00Z",
						"status":      "success",
					},
				},
			})
		})

		// TODO: 需要认证的路由（暂时注释）
		// authenticated := api.Group("")
		// authenticated.Use(middleware.AuthMiddleware(handlers.Auth.GetJWTManager()))
		// {
		// 	// TODO: 认证相关（暂时注释）
		// 	// authRoutes := authenticated.Group("/auth")
		// 	// {
		// 	// 	authRoutes.POST("/logout", handlers.Auth.Logout)
		// 	// 	authRoutes.GET("/profile", handlers.Auth.GetProfile)
		// 	// 	authRoutes.PUT("/profile", handlers.Auth.UpdateProfile)
		// 	// 	authRoutes.POST("/change-password", handlers.Auth.ChangePassword)
		// 	// }

		// 	// TODO: 用户管理（暂时注释）
		// 	// users := authenticated.Group("/users")
		// 	// users.Use(middleware.RequireRole("admin", "super_admin"))
		// 	// {
		// 	// 	users.GET("", handlers.User.ListUsers)
		// 	// 	users.POST("", handlers.User.CreateUser)
		// 	// 	users.GET("/:id", handlers.User.GetUser)
		// 	// 	users.PUT("/:id", handlers.User.UpdateUser)
		// 	// 	users.DELETE("/:id", handlers.User.DeleteUser)
		// 	// 	users.POST("/:id/reset-password", handlers.User.ResetPassword)
		// 	// 	users.POST("/:id/lock", handlers.User.LockUser)
		// 	// 	users.POST("/:id/unlock", handlers.User.UnlockUser)
		// 	// }

		// 	// TODO: 主机管理（暂时注释）
		// 	// hosts := authenticated.Group("/hosts")
		// 	// {
		// 	// 	hosts.GET("", handlers.Host.ListHosts)
		// 	// 	hosts.POST("", middleware.RequireRole("operator", "admin", "super_admin"), handlers.Host.CreateHost)
		// 	// 	hosts.GET("/:id", handlers.Host.GetHost)
		// 	// 	hosts.PUT("/:id", middleware.RequireRole("operator", "admin", "super_admin"), handlers.Host.UpdateHost)
		// 	// 	hosts.DELETE("/:id", middleware.RequireRole("admin", "super_admin"), handlers.Host.DeleteHost)
		// 	// 	hosts.POST("/:id/test", handlers.Host.TestConnection)
		// 	// 	hosts.POST("/:id/execute", middleware.RequireRole("operator", "admin", "super_admin"), handlers.Host.ExecuteCommand)
		// 	// }

		// 	// TODO: 告警管理（暂时注释）
		// 	// alerts := authenticated.Group("/alerts")
		// 	// {
		// 	// 	alerts.GET("", handlers.Alert.ListAlerts)
		// 	// 	alerts.POST("", middleware.RequireRole("operator", "admin", "super_admin"), handlers.Alert.CreateAlert)
		// 	// 	alerts.GET("/:id", handlers.Alert.GetAlert)
		// 	// 	alerts.PUT("/:id", middleware.RequireRole("operator", "admin", "super_admin"), handlers.Alert.UpdateAlert)
		// 	// 	alerts.DELETE("/:id", middleware.RequireRole("admin", "super_admin"), handlers.Alert.DeleteAlert)
		// 	// 	alerts.POST("/:id/acknowledge", middleware.RequireRole("operator", "admin", "super_admin"), handlers.Alert.AcknowledgeAlert)
		// 	// 	alerts.POST("/:id/resolve", middleware.RequireRole("operator", "admin", "super_admin"), handlers.Alert.ResolveAlert)
		// 	// 	alerts.POST("/:id/close", middleware.RequireRole("operator", "admin", "super_admin"), handlers.Alert.CloseAlert)
		// 	// 	alerts.GET("/summary", handlers.Alert.GetAlertSummary)
		// 	// }

		// 	// TODO: 操作日志（暂时注释）
		// 	// operations := authenticated.Group("/operations")
		// 	// {
		// 	// 	operations.GET("", handlers.Operation.ListOperations)
		// 	// 	operations.GET("/:id", handlers.Operation.GetOperation)
		// 	// 	operations.GET("/stats", handlers.Operation.GetOperationStats)
		// 	// }

		// 	// TODO: 系统配置（暂时注释）
		// 	// configs := authenticated.Group("/config")
		// 	// {
		// 	// 	configs.GET("", middleware.RequireRole("admin", "super_admin"), handlers.Config.GetAllConfigs)
		// 	// 	configs.GET("/:key", middleware.RequireRole("admin", "super_admin"), handlers.Config.GetConfig)
		// 	// 	configs.PUT("/:key", middleware.RequireRole("super_admin"), handlers.Config.UpdateConfig)
		// 	// }
		// }
	}

	// WebSocket路由
	ws := r.Group("/ws")
	ws.Use(middleware.OptionalAuthMiddleware(handlers.Auth.GetJWTManager()))
	{
		ws.GET("/chat", handlers.Chat.HandleWebSocket)
		// ws.GET("/notifications", handlers.Notification.HandleWebSocket) // 暂时注释通知功能
	}

	// 静态文件服务
	r.Static("/static", "./web/static")
	r.LoadHTMLGlob("web/templates/*")

	// 前端路由（无需认证）
	frontend := r.Group("")
	{
		frontend.GET("/", handlers.Frontend.Index)
		frontend.GET("/login", handlers.Frontend.Login)
		frontend.GET("/dashboard", handlers.Frontend.Dashboard)
		frontend.GET("/hosts", handlers.Frontend.Hosts)
		frontend.GET("/alerts", handlers.Frontend.Alerts)
		frontend.GET("/chat", handlers.Frontend.Chat)
		frontend.GET("/stats", handlers.Frontend.Stats)
		frontend.GET("/users", handlers.Frontend.Users)
		frontend.GET("/config", handlers.Frontend.Config)
		frontend.GET("/revolutionary-demo", handlers.Frontend.RevolutionaryDemo)
		frontend.GET("/chat-test", func(c *gin.Context) {
			c.HTML(http.StatusOK, "chat_test.html", gin.H{
				"title": "聊天功能测试",
			})
		})
	}

	// 404处理
	r.NoRoute(func(c *gin.Context) {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "Route not found",
		})
	})

	return r
}

// SetupRoutes 设置路由（备用函数）
func SetupRoutes(r *gin.Engine, cfg *config.Config, services *service.Services, logger *logrus.Logger) {
	// 这个函数可以用于更复杂的路由设置
	// 目前使用New函数即可
}

// RouteInfo 路由信息
type RouteInfo struct {
	Method      string   `json:"method"`
	Path        string   `json:"path"`
	Handler     string   `json:"handler"`
	Middlewares []string `json:"middlewares"`
}

// GetRoutes 获取所有路由信息
func GetRoutes(r *gin.Engine) []RouteInfo {
	routes := r.Routes()
	routeInfos := make([]RouteInfo, len(routes))

	for i, route := range routes {
		routeInfos[i] = RouteInfo{
			Method:  route.Method,
			Path:    route.Path,
			Handler: route.Handler,
		}
	}

	return routeInfos
}
