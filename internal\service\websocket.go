package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// WebSocketManager WebSocket管理器
type WebSocketManager struct {
	logger      *logrus.Logger
	upgrader    websocket.Upgrader
	connections map[string]*WebSocketConnection
	mutex       sync.RWMutex
	hub         *Hub
	chatService ChatService // 添加聊天服务引用
	aiService   AIService   // 添加AI服务引用（支持意图识别）
	db          *gorm.DB    // 添加数据库连接用于直接执行
}

// WebSocketConnection WebSocket连接
type WebSocketConnection struct {
	ID        string
	UserID    int64
	SessionID string
	Conn      *websocket.Conn
	Send      chan []byte
	Hub       *Hub
}

// WSMessage WebSocket消息
type WSMessage struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
}

// Hub WebSocket连接中心
type Hub struct {
	connections map[*WebSocketConnection]bool
	broadcast   chan []byte
	register    chan *WebSocketConnection
	unregister  chan *WebSocketConnection
	logger      *logrus.Logger
}

// SystemNotificationMessage 系统通知消息
type SystemNotificationMessage struct {
	Level     string                 `json:"level"`
	Title     string                 `json:"title"`
	Content   string                 `json:"content"`
	Timestamp time.Time              `json:"timestamp"`
	Actions   []NotificationAction   `json:"actions,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// NotificationAction 通知操作
type NotificationAction struct {
	ID    string `json:"id"`
	Label string `json:"label"`
	Type  string `json:"type"`
}

// CommandProgressMessage 命令进度消息
type CommandProgressMessage struct {
	Command     string                 `json:"command"`
	HostID      int64                  `json:"host_id"`
	Status      string                 `json:"status"`
	Progress    float64                `json:"progress"`
	Output      string                 `json:"output,omitempty"`
	Error       string                 `json:"error,omitempty"`
	StartTime   time.Time              `json:"start_time"`
	ElapsedTime string                 `json:"elapsed_time"`
	Timestamp   time.Time              `json:"timestamp"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// NewWebSocketManager 创建WebSocket管理器
func NewWebSocketManager(logger *logrus.Logger, db *gorm.DB) *WebSocketManager {
	hub := &Hub{
		connections: make(map[*WebSocketConnection]bool),
		broadcast:   make(chan []byte),
		register:    make(chan *WebSocketConnection),
		unregister:  make(chan *WebSocketConnection),
		logger:      logger,
	}

	manager := &WebSocketManager{
		logger:      logger,
		connections: make(map[string]*WebSocketConnection),
		hub:         hub,
		db:          db, // 添加数据库连接
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// 在生产环境中应该检查Origin
				return true
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
	}

	// 启动Hub
	go hub.Run()

	return manager
}

// SetChatService 设置聊天服务
func (wm *WebSocketManager) SetChatService(chatService ChatService) {
	wm.chatService = chatService
}

// SetAIService 设置AI服务
func (wm *WebSocketManager) SetAIService(aiService AIService) {
	wm.aiService = aiService
}

// HandleWebSocket 处理WebSocket连接
func (wm *WebSocketManager) HandleWebSocket(w http.ResponseWriter, r *http.Request, userID int64, sessionID string) error {
	conn, err := wm.upgrader.Upgrade(w, r, nil)
	if err != nil {
		return fmt.Errorf("failed to upgrade connection: %w", err)
	}

	// 生成连接ID
	connectionID := fmt.Sprintf("%d_%s_%d", userID, sessionID, time.Now().Unix())

	wsConn := &WebSocketConnection{
		ID:        connectionID,
		UserID:    userID,
		SessionID: sessionID,
		Conn:      conn,
		Send:      make(chan []byte, 256),
		Hub:       wm.hub,
	}

	// 注册连接
	wm.mutex.Lock()
	wm.connections[connectionID] = wsConn
	wm.mutex.Unlock()

	wm.hub.register <- wsConn

	wm.logger.WithFields(logrus.Fields{
		"connection_id": connectionID,
		"user_id":       userID,
		"session_id":    sessionID,
	}).Info("WebSocket connection established")

	// 启动读写协程
	go wm.writePump(wsConn)
	go wm.readPump(wsConn)

	return nil
}

// readPump 读取消息
func (wm *WebSocketManager) readPump(conn *WebSocketConnection) {
	defer func() {
		wm.hub.unregister <- conn
		conn.Conn.Close()
		wm.mutex.Lock()
		delete(wm.connections, conn.ID)
		wm.mutex.Unlock()
	}()

	conn.Conn.SetReadLimit(512)
	conn.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	conn.Conn.SetPongHandler(func(string) error {
		conn.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, message, err := conn.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				wm.logger.WithError(err).Error("WebSocket unexpected close error")
			}
			break
		}

		// 先尝试解析为通用的map，以便调试
		var rawMsg map[string]interface{}
		if err := json.Unmarshal(message, &rawMsg); err != nil {
			wm.logger.WithError(err).Error("Failed to unmarshal raw WebSocket message")
			continue
		}

		wm.logger.WithFields(logrus.Fields{
			"connection_id": conn.ID,
			"raw_message":   rawMsg,
			"message_size":  len(message),
		}).Info("🔥 WebSocket: 收到原始消息")

		var msg WSMessage
		if err := json.Unmarshal(message, &msg); err != nil {
			wm.logger.WithError(err).Error("Failed to unmarshal WebSocket message")
			continue
		}

		// 如果Data为空但原始消息中有内容，尝试提取
		if msg.Data == nil && rawMsg != nil {
			// 检查是否有content字段
			if content, exists := rawMsg["content"]; exists {
				msg.Data = content
				wm.logger.WithField("extracted_content", content).Info("WebSocket: 从原始消息中提取到内容")

				// 🚀 超级紧急修复：直接处理"列出主机"消息
				if contentStr, ok := content.(string); ok && strings.Contains(strings.ToLower(contentStr), "列出主机") {
					wm.logger.Info("WebSocket: 🎯 readPump检测到列出主机请求，直接处理")

					// 直接查询数据库获取主机列表
					if wm.db != nil {
						var hosts []struct {
							ID       uint   `json:"id"`
							IP       string `json:"ip"`
							Username string `json:"username"`
							Status   string `json:"status"`
							Env      string `json:"env"`
						}

						err := wm.db.Table("hosts").Select("id, ip, username, status, env").Find(&hosts).Error
						if err == nil {
							// 构建主机列表响应
							content := "📋 **主机列表查询结果**\n\n"
							content += "| 主机ID | IP地址 | 用户名 | 状态 | 环境 |\n"
							content += "|--------|--------|--------|------|------|\n"

							onlineCount := 0
							offlineCount := 0

							for _, host := range hosts {
								status := "🔴离线"
								if host.Status == "online" {
									status = "🟢在线"
									onlineCount++
								} else {
									offlineCount++
								}

								content += fmt.Sprintf("| %d | %s | %s | %s | %s |\n",
									host.ID, host.IP, host.Username, status, host.Env)
							}

							content += fmt.Sprintf("\n📊 **统计信息**：\n🟢 在线: %d台 | 🔴 离线: %d台 | 📊 总计: %d台",
								onlineCount, offlineCount, len(hosts))

							// 发送响应
							responseMsg := &WSMessage{
								Type: "assistant_message",
								Data: map[string]interface{}{
									"content":     content,
									"intent":      "database_operations",
									"confidence":  0.95,
									"created_at":  time.Now(),
									"token_count": len(content) / 4,
									"source":      "direct_websocket_fix",
								},
								Timestamp: time.Now(),
							}

							// 发送用户消息确认
							wm.SendToConnection(conn.ID, &WSMessage{
								Type: "user_message",
								Data: map[string]interface{}{
									"content":    contentStr,
									"created_at": time.Now(),
								},
								Timestamp: time.Now(),
							})

							// 发送AI响应
							wm.SendToConnection(conn.ID, responseMsg)

							// 跳过后续处理
							continue
						} else {
							wm.logger.WithError(err).Error("WebSocket: 查询主机列表失败")
						}
					}
				}
			} else if data, exists := rawMsg["data"]; exists {
				msg.Data = data
				wm.logger.WithField("extracted_data", data).Info("WebSocket: 从原始消息中提取到data字段")
			}
		}

		wm.handleMessage(conn, &msg)
	}
}

// writePump 写入消息
func (wm *WebSocketManager) writePump(conn *WebSocketConnection) {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		conn.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-conn.Send:
			conn.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				conn.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := conn.Conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			n := len(conn.Send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-conn.Send)
			}

			if err := w.Close(); err != nil {
				return
			}
		case <-ticker.C:
			conn.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := conn.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理消息
func (wm *WebSocketManager) handleMessage(conn *WebSocketConnection, msg *WSMessage) {
	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"message_type":  msg.Type,
		"data_type":     fmt.Sprintf("%T", msg.Data),
		"data_value":    msg.Data,
		"timestamp":     msg.Timestamp,
	}).Info("🔥 WebSocket: handleMessage 收到消息")

	// 根据消息类型处理
	switch msg.Type {
	case "ping":
		wm.SendToConnection(conn.ID, &WSMessage{
			Type:      "pong",
			Data:      "pong",
			Timestamp: time.Now(),
		})
	case "message":
		// 处理聊天消息
		wm.handleChatMessage(conn, *msg)
	case "subscribe":
		// 处理订阅请求
		wm.handleSubscription(conn, msg.Data)
	case "unsubscribe":
		// 处理取消订阅请求
		wm.handleUnsubscription(conn, msg.Data)
	default:
		wm.logger.WithField("message_type", msg.Type).Warn("Unknown message type")
	}
}

// handleSubscription 处理订阅
func (wm *WebSocketManager) handleSubscription(conn *WebSocketConnection, data interface{}) {
	// 实现订阅逻辑
	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"subscription":  data,
	}).Debug("Handling subscription")
}

// handleUnsubscription 处理取消订阅
func (wm *WebSocketManager) handleUnsubscription(conn *WebSocketConnection, data interface{}) {
	// 实现取消订阅逻辑
	wm.logger.WithFields(logrus.Fields{
		"connection_id":  conn.ID,
		"unsubscription": data,
	}).Debug("Handling unsubscription")
}

// handleChatMessage 处理聊天消息
func (wm *WebSocketManager) handleChatMessage(conn *WebSocketConnection, msg WSMessage) {
	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
		"message_type":  msg.Type,
		"data_type":     fmt.Sprintf("%T", msg.Data),
	}).Info("🔥 WebSocket: handleChatMessage 被调用")

	// 检查聊天服务是否可用
	if wm.chatService == nil {
		wm.SendToConnection(conn.ID, &WSMessage{
			Type:      "error",
			Data:      "Chat service not available",
			Timestamp: time.Now(),
		})
		return
	}

	// 提取消息内容 - 支持多种格式
	var content string

	// 尝试直接转换为字符串
	if str, ok := msg.Data.(string); ok {
		content = str
	} else if msg.Data == nil {
		// 如果Data为空，尝试从消息的其他字段获取内容
		wm.logger.WithFields(logrus.Fields{
			"connection_id": conn.ID,
			"msg_type":      msg.Type,
			"msg_data":      msg.Data,
		}).Warn("WebSocket: Data字段为空，尝试其他方式获取消息内容")

		// 检查是否有content字段
		if msgMap, ok := msg.Data.(map[string]interface{}); ok {
			if contentVal, exists := msgMap["content"]; exists {
				if contentStr, ok := contentVal.(string); ok {
					content = contentStr
				}
			}
		}

		// 如果仍然没有内容，返回错误
		if content == "" {
			wm.logger.Error("WebSocket: ❌ 无法从消息中提取内容")
			wm.SendToConnection(conn.ID, &WSMessage{
				Type:      "error",
				Data:      "No message content found",
				Timestamp: time.Now(),
			})
			return
		}
	} else {
		// 尝试将其他类型转换为字符串
		content = fmt.Sprintf("%v", msg.Data)
		wm.logger.WithFields(logrus.Fields{
			"connection_id":     conn.ID,
			"data_type":         fmt.Sprintf("%T", msg.Data),
			"converted_content": content,
		}).Warn("WebSocket: 消息类型不是字符串，已转换")
	}

	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
		"content":       content,
		"content_lower": strings.ToLower(content),
	}).Info("🔥 WebSocket: 成功提取消息内容")

	// 发送用户消息确认
	wm.SendToConnection(conn.ID, &WSMessage{
		Type: "user_message",
		Data: map[string]interface{}{
			"content":    content,
			"created_at": time.Now(),
		},
		Timestamp: time.Now(),
	})

	// 🚀 紧急修复：直接处理"列出主机"消息
	if strings.Contains(strings.ToLower(content), "列出主机") {
		wm.logger.Info("WebSocket: 🎯 检测到列出主机请求，直接处理")

		// 直接查询数据库获取主机列表
		if wm.db != nil {
			var hosts []struct {
				ID       uint   `json:"id"`
				IP       string `json:"ip"`
				Username string `json:"username"`
				Status   string `json:"status"`
				Env      string `json:"env"`
			}

			err := wm.db.Table("hosts").Select("id, ip, username, status, env").Find(&hosts).Error
			if err == nil {
				// 构建主机列表响应
				content := "📋 **主机列表查询结果**\n\n"
				content += "| 主机ID | IP地址 | 用户名 | 状态 | 环境 |\n"
				content += "|--------|--------|--------|------|------|\n"

				onlineCount := 0
				offlineCount := 0

				for _, host := range hosts {
					status := "🔴离线"
					if host.Status == "online" {
						status = "🟢在线"
						onlineCount++
					} else {
						offlineCount++
					}

					content += fmt.Sprintf("| %d | %s | %s | %s | %s |\n",
						host.ID, host.IP, host.Username, status, host.Env)
				}

				content += fmt.Sprintf("\n📊 **统计信息**：\n🟢 在线: %d台 | 🔴 离线: %d台 | 📊 总计: %d台",
					onlineCount, offlineCount, len(hosts))

				// 发送响应
				wm.SendToConnection(conn.ID, &WSMessage{
					Type: "assistant_message",
					Data: map[string]interface{}{
						"content":     content,
						"intent":      "database_operations",
						"confidence":  0.95,
						"created_at":  time.Now(),
						"token_count": len(content) / 4,
						"source":      "direct_websocket_fix",
					},
					Timestamp: time.Now(),
				})
				return
			} else {
				wm.logger.WithError(err).Error("WebSocket: 查询主机列表失败")
			}
		}
	}

	// 使用智能AI服务处理消息（支持意图识别和执行）
	wm.logger.WithFields(logrus.Fields{
		"connection_id":   conn.ID,
		"session_id":      conn.SessionID,
		"message":         content,
		"ai_service_nil":  wm.aiService == nil,
		"ai_service_type": fmt.Sprintf("%T", wm.aiService),
	}).Info("🔥 WebSocket: 准备调用AI服务处理消息")

	if wm.aiService != nil {
		wm.logger.Info("WebSocket: ✅ AI服务可用，调用handleWithAIService")
		wm.handleWithAIService(conn, content)
	} else {
		wm.logger.Error("WebSocket: ❌ AI服务为空，使用降级处理")
		// 降级到基础聊天服务
		wm.handleWithChatService(conn, content)
	}
}

// handleWithAIService 使用AI服务处理消息（支持意图识别）
func (wm *WebSocketManager) handleWithAIService(conn *WebSocketConnection, content string) {
	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
		"user_id":       conn.UserID,
		"message":       content,
	}).Info("WebSocket: Starting AI service message processing")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 🚀 临时禁用DirectExecutionHandler，直接使用统一AI服务
	wm.logger.WithField("message", content).Info("WebSocket: 🔧 跳过DirectExecutionHandler，直接使用统一AI服务进行调试")

	// 构建AI服务请求
	req := &ProcessMessageRequest{
		SessionID: conn.SessionID,
		UserID:    conn.UserID,
		Message:   content,
	}

	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
		"ai_service":    fmt.Sprintf("%T", wm.aiService),
	}).Info("WebSocket: 🚀 调用统一AI服务处理消息")

	// 调用AI服务处理消息
	response, err := wm.aiService.ProcessMessage(ctx, req)
	if err != nil {
		wm.logger.WithFields(logrus.Fields{
			"connection_id": conn.ID,
			"session_id":    conn.SessionID,
			"error":         err.Error(),
			"ai_service":    fmt.Sprintf("%T", wm.aiService),
		}).Error("WebSocket: ❌ 统一AI服务处理失败")

		wm.SendToConnection(conn.ID, &WSMessage{
			Type:      "error",
			Data:      "Failed to process message",
			Timestamp: time.Now(),
		})
		return
	}

	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
		"content":       response.Content,
		"intent":        response.Intent,
		"confidence":    response.Confidence,
		"token_count":   response.TokenCount,
	}).Info("WebSocket: AI service returned response, preparing to send")

	// 发送AI响应 - 避免循环引用
	responseData := map[string]interface{}{
		"content":     response.Content,
		"intent":      response.Intent,
		"confidence":  response.Confidence,
		"created_at":  response.Timestamp,
		"token_count": response.TokenCount,
	}

	// 安全地添加参数，避免循环引用
	if response.Parameters != nil {
		safeParams := make(map[string]interface{})
		for k, v := range response.Parameters {
			// 只添加基本类型，避免复杂对象的循环引用
			switch val := v.(type) {
			case string, int, int64, float64, bool:
				safeParams[k] = val
			case nil:
				safeParams[k] = nil
			default:
				// 对于复杂类型，转换为字符串
				safeParams[k] = fmt.Sprintf("%v", val)
			}
		}
		responseData["parameters"] = safeParams
	}

	responseMsg := &WSMessage{
		Type:      "assistant_message",
		Data:      responseData,
		Timestamp: time.Now(),
	}

	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
		"message_type":  responseMsg.Type,
	}).Info("WebSocket: Sending AI response to connection")

	wm.SendToConnection(conn.ID, responseMsg)

	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
	}).Info("WebSocket: AI response sent successfully")
}

// handleWithChatService 使用基础聊天服务处理消息
func (wm *WebSocketManager) handleWithChatService(conn *WebSocketConnection, content string) {
	// 基础聊天服务处理逻辑
	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
		"message":       content,
	}).Info("WebSocket: Using basic chat service")

	// 这里可以实现基础聊天逻辑
	response := "收到您的消息：" + content

	wm.SendToConnection(conn.ID, &WSMessage{
		Type: "assistant_message",
		Data: map[string]interface{}{
			"content":    response,
			"created_at": time.Now(),
		},
		Timestamp: time.Now(),
	})
}

// SendToConnection 发送消息到指定连接
func (wm *WebSocketManager) SendToConnection(connectionID string, message *WSMessage) error {
	wm.mutex.RLock()
	conn, exists := wm.connections[connectionID]
	wm.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("connection %s not found", connectionID)
	}

	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	select {
	case conn.Send <- data:
		return nil
	default:
		close(conn.Send)
		wm.mutex.Lock()
		delete(wm.connections, connectionID)
		wm.mutex.Unlock()
		return fmt.Errorf("connection %s send channel is full", connectionID)
	}
}

// SendToUser 发送消息到用户的所有连接
func (wm *WebSocketManager) SendToUser(userID int64, message *WSMessage) error {
	wm.mutex.RLock()
	defer wm.mutex.RUnlock()

	sent := 0
	for _, conn := range wm.connections {
		if conn.UserID == userID {
			if err := wm.SendToConnection(conn.ID, message); err != nil {
				wm.logger.WithError(err).WithField("connection_id", conn.ID).Warn("Failed to send message to connection")
			} else {
				sent++
			}
		}
	}

	if sent == 0 {
		return fmt.Errorf("no active connections found for user %d", userID)
	}

	return nil
}

// SendToSession 发送消息到指定会话
func (wm *WebSocketManager) SendToSession(sessionID string, message *WSMessage) error {
	wm.mutex.RLock()
	defer wm.mutex.RUnlock()

	for _, conn := range wm.connections {
		if conn.SessionID == sessionID {
			return wm.SendToConnection(conn.ID, message)
		}
	}

	return fmt.Errorf("no active connection found for session %s", sessionID)
}

// BroadcastToAll 广播消息到所有连接
func (wm *WebSocketManager) BroadcastToAll(message *WSMessage) error {
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	wm.hub.broadcast <- data
	return nil
}

// GetActiveConnections 获取活跃连接数
func (wm *WebSocketManager) GetActiveConnections() int {
	wm.mutex.RLock()
	defer wm.mutex.RUnlock()
	return len(wm.connections)
}

// GetConnectionsByUser 获取用户的连接数
func (wm *WebSocketManager) GetConnectionsByUser(userID int64) int {
	wm.mutex.RLock()
	defer wm.mutex.RUnlock()

	count := 0
	for _, conn := range wm.connections {
		if conn.UserID == userID {
			count++
		}
	}
	return count
}

// SendSystemNotification 发送系统通知
func (wm *WebSocketManager) SendSystemNotification(userID int64, notification *SystemNotificationMessage) error {
	// 设置时间戳
	notification.Timestamp = time.Now()

	// 创建WebSocket消息
	message := &WSMessage{
		Type: "system_notification",
		Data: notification,
	}

	// 发送给用户的所有连接
	return wm.SendToUser(userID, message)
}

// SendCommandProgress 发送命令执行进度
func (wm *WebSocketManager) SendCommandProgress(sessionID string, progress *CommandProgressMessage) error {
	// 设置时间戳
	progress.Timestamp = time.Now()

	// 创建WebSocket消息
	message := &WSMessage{
		Type: "command_progress",
		Data: progress,
	}

	// 发送给指定会话
	return wm.SendToSession(sessionID, message)
}

// ConnectionStats 连接统计信息
type ConnectionStats struct {
	TotalConnections int                    `json:"total_connections"`
	UserConnections  map[int64]int          `json:"user_connections"`
	SessionStats     map[string]interface{} `json:"session_stats"`
}

// GetConnectionStats 获取连接统计
func (wm *WebSocketManager) GetConnectionStats() *ConnectionStats {
	wm.mutex.RLock()
	defer wm.mutex.RUnlock()

	userConnections := make(map[int64]int)
	sessionStats := make(map[string]interface{})

	for _, conn := range wm.connections {
		userConnections[conn.UserID]++
		if conn.SessionID != "" {
			sessionStats[conn.SessionID] = map[string]interface{}{
				"user_id": conn.UserID,
			}
		}
	}

	return &ConnectionStats{
		TotalConnections: len(wm.connections),
		UserConnections:  userConnections,
		SessionStats:     sessionStats,
	}
}

// Run Hub运行
func (h *Hub) Run() {
	for {
		select {
		case conn := <-h.register:
			h.connections[conn] = true
			h.logger.WithField("connection_id", conn.ID).Debug("Connection registered")

		case conn := <-h.unregister:
			if _, ok := h.connections[conn]; ok {
				delete(h.connections, conn)
				close(conn.Send)
				h.logger.WithField("connection_id", conn.ID).Debug("Connection unregistered")
			}

		case message := <-h.broadcast:
			for conn := range h.connections {
				select {
				case conn.Send <- message:
				default:
					close(conn.Send)
					delete(h.connections, conn)
				}
			}
		}
	}
}
