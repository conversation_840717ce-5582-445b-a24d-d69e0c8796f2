<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI运维助手 - 智能对话管理平台</title>

    <!-- 现代化Sider风格样式 -->
    <link href="/static/css/sider-style.css" rel="stylesheet">

    <!-- 图标 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Sider风格的现代化应用容器 -->
    <div class="sider-app">
        <!-- 左侧边栏 -->
        <aside class="sider-sidebar">
            <!-- 品牌标识 -->
            <div class="sider-brand">
                <div class="brand-icon">
                    <i class="bi bi-robot"></i>
                </div>
                <span class="brand-text">AI运维助手</span>
            </div>

            <!-- 导航菜单 -->
            <nav class="sider-nav">
                <div class="nav-item active" onclick="switchToChat()">
                    <i class="bi bi-chat-dots"></i>
                    <span>聊天</span>
                </div>
                <div class="nav-item" onclick="switchToHosts()">
                    <i class="bi bi-server"></i>
                    <span>主机</span>
                </div>
                <div class="nav-item" onclick="switchToMonitoring()">
                    <i class="bi bi-activity"></i>
                    <span>监控</span>
                </div>
                <div class="nav-item" onclick="switchToAlerts()">
                    <i class="bi bi-exclamation-triangle"></i>
                    <span>告警</span>
                </div>
                <div class="nav-item" onclick="switchToReports()">
                    <i class="bi bi-graph-up"></i>
                    <span>报表</span>
                </div>
                <div class="nav-item" onclick="switchToTerminal()">
                    <i class="bi bi-terminal"></i>
                    <span>终端</span>
                </div>
                <div class="nav-item" onclick="switchToFiles()">
                    <i class="bi bi-folder"></i>
                    <span>文件</span>
                </div>
                <div class="nav-item" onclick="switchToSettings()">
                    <i class="bi bi-gear"></i>
                    <span>设置</span>
                </div>
            </nav>

            <!-- 底部用户信息 -->
            <div class="sider-user">
                <div class="user-avatar">A</div>
                <div class="user-info">
                    <div class="user-name">管理员</div>
                    <div class="user-status">在线</div>
                </div>
            </div>
        </aside>

        <!-- 主内容区域 -->
        <main class="sider-main">
            <!-- 聊天界面 -->
            <div class="chat-view active" id="chat-view">
                <!-- 对话消息区域 -->
                <div class="chat-messages" id="chat-messages">
                    <!-- 欢迎界面 -->
                    <div class="welcome-screen" id="welcome-screen">
                        <div class="welcome-content">
                            <h1 class="welcome-title">你好，</h1>
                            <p class="welcome-subtitle">我今天能帮你什么？</p>

                            <!-- 快捷建议 -->
                            <div class="quick-suggestions">
                                <button class="suggestion-chip" onclick="insertSuggestion('查看主机状态')">
                                    <i class="bi bi-server"></i>
                                    查看主机状态
                                </button>
                                <button class="suggestion-chip" onclick="insertSuggestion('检查系统告警')">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    检查系统告警
                                </button>
                                <button class="suggestion-chip" onclick="insertSuggestion('生成运维报表')">
                                    <i class="bi bi-graph-up"></i>
                                    生成运维报表
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部输入区域 -->
                <div class="chat-input-container">
                    <!-- 顶部控制栏：模型选择器和功能按钮 -->
                    <div class="input-controls">
                        <!-- 左侧模型选择器 -->
                        <div class="model-selector" onclick="toggleModelSelector()">
                            <div class="model-icon">
                                <span id="current-model-icon">🤖</span>
                            </div>
                            <span class="model-name" id="current-model-name">加载中...</span>
                            <i class="bi bi-chevron-down"></i>
                        </div>

                        <!-- 模型选择下拉菜单 -->
                        <div class="model-dropdown" id="model-dropdown" style="display: none;">
                            <div class="model-dropdown-content" id="model-dropdown-content">
                                <!-- 动态加载模型列表 -->
                            </div>
                        </div>

                        <!-- 右侧功能按钮 -->
                        <div class="input-actions">
                            <button class="action-btn" onclick="toggleHistoryPanel()" title="聊天历史">
                                <i class="bi bi-chat-dots"></i>
                            </button>
                            <button class="action-btn new-chat-btn" onclick="startNewChat()" title="新建对话">
                                <i class="bi bi-plus"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 输入框区域 -->
                    <div class="input-wrapper">
                        <!-- 输入框 -->
                        <textarea
                            id="chat-input"
                            placeholder="向我询问任何问题..."
                            rows="3"
                            onkeydown="handleInputKeydown(event)"
                            oninput="adjustTextareaHeight(this)"
                        ></textarea>
                    </div>
                </div>
            </div>

            <!-- 其他视图（主机、监控等） -->
            <div class="hosts-view" id="hosts-view" style="display: none;">
                <div class="view-header">
                    <h2>主机管理</h2>
                    <p>管理和监控您的服务器</p>
                </div>
                <div class="view-content">
                    <div class="placeholder-content">
                        <i class="bi bi-server"></i>
                        <p>主机管理功能正在开发中...</p>
                    </div>
                </div>
            </div>

            <div class="monitoring-view" id="monitoring-view" style="display: none;">
                <div class="view-header">
                    <h2>系统监控</h2>
                    <p>实时监控系统性能和状态</p>
                </div>
                <div class="view-content">
                    <div class="placeholder-content">
                        <i class="bi bi-activity"></i>
                        <p>监控功能正在开发中...</p>
                    </div>
                </div>
            </div>

            <div class="alerts-view" id="alerts-view" style="display: none;">
                <div class="view-header">
                    <h2>告警管理</h2>
                    <p>查看和处理系统告警</p>
                </div>
                <div class="view-content">
                    <div class="placeholder-content">
                        <i class="bi bi-exclamation-triangle"></i>
                        <p>告警管理功能正在开发中...</p>
                    </div>
                </div>
            </div>

            <div class="reports-view" id="reports-view" style="display: none;">
                <div class="view-header">
                    <h2>统计报表</h2>
                    <p>生成和查看运维统计报表</p>
                </div>
                <div class="view-content">
                    <div class="placeholder-content">
                        <i class="bi bi-graph-up"></i>
                        <p>报表功能正在开发中...</p>
                    </div>
                </div>
            </div>

            <div class="terminal-view" id="terminal-view" style="display: none;">
                <div class="view-header">
                    <h2>远程终端</h2>
                    <p>远程执行命令和脚本</p>
                </div>
                <div class="view-content">
                    <div class="placeholder-content">
                        <i class="bi bi-terminal"></i>
                        <p>终端功能正在开发中...</p>
                    </div>
                </div>
            </div>

            <div class="files-view" id="files-view" style="display: none;">
                <div class="view-header">
                    <h2>文件管理</h2>
                    <p>管理服务器文件和配置</p>
                </div>
                <div class="view-content">
                    <div class="placeholder-content">
                        <i class="bi bi-folder"></i>
                        <p>文件管理功能正在开发中...</p>
                    </div>
                </div>
            </div>

            <div class="settings-view" id="settings-view" style="display: none;">
                <div class="view-header">
                    <h2>系统设置</h2>
                    <p>配置系统参数和偏好设置</p>
                </div>
                <div class="view-content">
                    <div class="placeholder-content">
                        <i class="bi bi-gear"></i>
                        <p>设置功能正在开发中...</p>
                    </div>
                </div>
            </div>
        </main>

        <!-- 右侧聊天历史面板 -->
        <aside class="chat-history-panel" id="chat-history-panel">
            <div class="history-header">
                <h3>聊天历史</h3>
                <button class="close-history-btn" onclick="toggleHistoryPanel()">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div class="history-content">
                <div class="history-search">
                    <input type="text" placeholder="搜索对话..." class="history-search-input">
                </div>
                <div class="history-list" id="history-list">
                    <!-- 历史对话项目将在这里动态生成 -->
                    <div class="history-item">
                        <div class="history-item-title">今天遇到的问题，我想要解决这个问题，但是...</div>
                        <div class="history-item-time">今天 14:30</div>
                    </div>
                    <div class="history-item">
                        <div class="history-item-title">今天遇到的问题，我想要解决这个问题，但是...</div>
                        <div class="history-item-time">今天 13:15</div>
                    </div>
                    <div class="history-item">
                        <div class="history-item-title">今天遇到的问题，我想要解决这个问题，但是...</div>
                        <div class="history-item-time">今天 12:45</div>
                    </div>
                    <div class="history-item">
                        <div class="history-item-title">数据库连接问题排查，需要检查连接池配置...</div>
                        <div class="history-item-time">昨天 16:20</div>
                    </div>
                    <div class="history-item">
                        <div class="history-item-title">服务器性能监控告警处理，CPU使用率过高...</div>
                        <div class="history-item-time">昨天 15:10</div>
                    </div>
                    <div class="history-item">
                        <div class="history-item-title">网络连接异常排查，ping测试失败...</div>
                        <div class="history-item-time">昨天 14:30</div>
                    </div>
                </div>
            </div>
        </aside>
    </div>



    <!-- Sider风格的JavaScript功能 -->
    <script>
        // 全局变量
        let currentUser = null;
        let chatMessages = [];
        let isTyping = false;
        let currentSessionId = null;
        let currentSessionTitle = '新对话';
        let currentModel = null;
        let availableModels = [];
        let chatSessions = [];
        let currentView = 'chat';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        // 模型管理功能 - 提前定义
        async function loadAvailableModels() {
            try {
                console.log('🔄 开始加载可用模型...');
                const response = await fetch('/api/v1/models/available');
                const result = await response.json();

                if (result.success) {
                    availableModels = result.data.models;
                    currentModel = result.data.models.find(m => m.is_current) || result.data.models[0];
                    console.log('✅ 模型加载成功:', currentModel);
                    updateModelSelector();
                    updateModelDropdown();
                } else {
                    console.error('Failed to load models:', result.error);
                    // 使用默认模型
                    currentModel = {
                        name: 'deepseek-chat',
                        display_name: 'DeepSeek Chat',
                        icon: '🤖',
                        description: 'DeepSeek智能对话模型'
                    };
                    updateModelSelector();
                }
            } catch (error) {
                console.error('Error loading models:', error);
                // 使用默认模型
                currentModel = {
                    name: 'deepseek-chat',
                    display_name: 'DeepSeek Chat',
                    icon: '🤖',
                    description: 'DeepSeek智能对话模型'
                };
                updateModelSelector();
            }
        }

        function updateModelSelector() {
            console.log('🔄 更新模型选择器，当前模型:', currentModel);
            if (currentModel) {
                const modelIcon = document.getElementById('current-model-icon');
                const modelName = document.getElementById('current-model-name');

                if (modelIcon) {
                    modelIcon.textContent = currentModel.icon || '🤖';
                    console.log('✅ 更新模型图标:', currentModel.icon);
                }
                if (modelName) {
                    modelName.textContent = currentModel.display_name || currentModel.name;
                    console.log('✅ 更新模型名称:', currentModel.display_name || currentModel.name);
                }
            } else {
                console.warn('⚠️ currentModel 为空，无法更新模型选择器');
            }
        }

        function updateModelDropdown() {
            const dropdown = document.getElementById('model-dropdown-content');
            if (!dropdown || !availableModels.length) return;

            dropdown.innerHTML = availableModels.map(model => `
                <div class="model-option ${model.is_current ? 'current' : ''}"
                     onclick="switchModel('${model.name}')">
                    <div class="model-option-icon">${model.icon || '🤖'}</div>
                    <div class="model-option-info">
                        <div class="model-option-name">${model.display_name}</div>
                        <div class="model-option-desc">${model.description || ''}</div>
                    </div>
                    <div class="model-option-provider">${model.provider}</div>
                </div>
            `).join('');
        }

        function toggleModelSelector() {
            const dropdown = document.getElementById('model-dropdown');
            if (dropdown) {
                dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
            }
        }

        async function switchModel(modelName) {
            try {
                const response = await fetch('/api/v1/models/switch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ model_name: modelName })
                });

                const result = await response.json();

                if (result.success) {
                    currentModel = result.data;
                    updateModelSelector();

                    // 更新下拉菜单中的当前状态
                    availableModels.forEach(model => {
                        model.is_current = model.name === modelName;
                    });
                    updateModelDropdown();

                    // 隐藏下拉菜单
                    toggleModelSelector();

                    console.log('Model switched to:', currentModel.display_name);
                } else {
                    console.error('Failed to switch model:', result.error);
                    alert('切换模型失败: ' + result.error);
                }
            } catch (error) {
                console.error('Error switching model:', error);
                alert('切换模型时发生错误');
            }
        }

        // 初始化应用
        function initializeApp() {
            console.log('Initializing Sider-style AI Operations Platform...');
            initializeChat();
            setupEventListeners();
            connectWebSocket();
            loadAvailableModels();

            // 延迟强制更新模型选择器，确保DOM已加载
            setTimeout(() => {
                if (currentModel) {
                    console.log('🔄 强制更新模型选择器');
                    updateModelSelector();
                }
            }, 1000);
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 键盘快捷键
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'n') {
                    e.preventDefault();
                    startNewChat();
                }
            });
        }

        // 视图切换功能
        function switchToChat() {
            switchView('chat');
        }

        function switchToHosts() {
            switchView('hosts');
        }

        function switchToMonitoring() {
            switchView('monitoring');
        }

        function switchToAlerts() {
            switchView('alerts');
        }

        function switchToReports() {
            switchView('reports');
        }

        function switchToTerminal() {
            switchView('terminal');
        }

        function switchToFiles() {
            switchView('files');
        }

        function switchToSettings() {
            switchView('settings');
        }

        function switchView(viewName) {
            // 隐藏所有视图
            const views = ['chat', 'hosts', 'monitoring', 'alerts', 'reports', 'terminal', 'files', 'settings'];
            views.forEach(view => {
                const element = document.getElementById(view + '-view');
                if (element) {
                    element.style.display = 'none';
                }
            });

            // 显示目标视图
            const targetView = document.getElementById(viewName + '-view');
            if (targetView) {
                targetView.style.display = 'flex';
            }

            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            const activeNavItem = document.querySelector(`.nav-item[onclick*="${viewName}"]`);
            if (activeNavItem) {
                activeNavItem.classList.add('active');
            }

            currentView = viewName;
        }

        // 初始化聊天功能
        function initializeChat() {
            console.log('Initializing chat...');

            // 如果没有当前会话ID，创建一个新的
            if (!currentSessionId) {
                currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                currentSessionTitle = '新对话';
                chatMessages = [];
            }

            showWelcomeScreen();
            console.log('Chat initialized with session:', currentSessionId);
        }



        // 显示欢迎界面
        function showWelcomeScreen() {
            const welcomeScreen = document.getElementById('welcome-screen');
            if (welcomeScreen) {
                welcomeScreen.style.display = 'flex';
            }
        }

        // 隐藏欢迎界面
        function hideWelcomeScreen() {
            const welcomeScreen = document.getElementById('welcome-screen');
            if (welcomeScreen) {
                welcomeScreen.style.display = 'none';
            }
        }

        // 插入建议文本
        function insertSuggestion(text) {
            const input = document.getElementById('chat-input');
            if (input) {
                input.value = text;
                input.focus();
                adjustTextareaHeight(input);
            }
        }

        // 自动调整文本框高度
        function adjustTextareaHeight(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        // 处理输入框按键事件
        function handleInputKeydown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 发送消息
        function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            if (!message) return;

            // 防止重复发送
            if (isWaitingForResponse) {
                console.log('正在等待响应，请稍候...');
                return;
            }

            // 隐藏欢迎界面
            hideWelcomeScreen();

            // 添加用户消息
            addMessage('user', message);

            // 清空输入框
            input.value = '';
            adjustTextareaHeight(input);

            // 设置等待状态
            isWaitingForResponse = true;

            // 显示AI正在输入
            showTypingIndicator();

            // 设置超时机制（30秒）
            typingTimeout = setTimeout(() => {
                hideTypingIndicator();
                addMessage('assistant', '抱歉，AI响应超时，请重试。');
                isWaitingForResponse = false;
            }, 30000);

            // 发送到后端
            if (!sendWebSocketMessage(message)) {
                // WebSocket不可用时的处理
                hideTypingIndicator();
                addMessage('assistant', '连接失败，请检查网络连接后重试。');
                isWaitingForResponse = false;
            }
        }

        // 添加消息到聊天界面
        function addMessage(sender, content) {
            const messagesContainer = document.getElementById('chat-messages');

            // 创建消息元素
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            // 如果是AI消息，添加头像和模型信息
            if (sender === 'assistant') {
                const messageHeader = document.createElement('div');
                messageHeader.className = 'message-header';
                messageHeader.innerHTML = `
                    <div class="message-avatar">
                        <i class="bi bi-robot"></i>
                    </div>
                    <span class="message-sender">${currentModel ? currentModel.display_name : 'AI助手'}</span>
                `;
                messageDiv.appendChild(messageHeader);
            }

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.innerHTML = formatMessageContent(content);

            messageDiv.appendChild(messageContent);
            messagesContainer.appendChild(messageDiv);

            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // 保存到历史记录
            chatMessages.push({
                sender: sender,
                content: content,
                timestamp: new Date().toISOString()
            });

            // 自动保存会话
            if (chatMessages.length % 3 === 0) {
                saveCurrentSession();
            }
        }

        // 格式化消息内容
        function formatMessageContent(content) {
            // 转义HTML并处理换行
            const escapeHtml = (text) => {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            };

            let formatted = escapeHtml(content).replace(/\n/g, '<br>');

            // 处理代码块
            formatted = formatted.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
                return `<div class="code-block"><pre><code>${code.trim()}</code></pre></div>`;
            });

            // 处理行内代码
            formatted = formatted.replace(/`([^`]+)`/g, '<code>$1</code>');

            return formatted;
        }

        // 显示输入指示器
        function showTypingIndicator() {
            const messagesContainer = document.getElementById('chat-messages');

            // 移除现有的输入指示器
            const existingIndicator = document.getElementById('typing-indicator');
            if (existingIndicator) {
                existingIndicator.remove();
            }

            const typingDiv = document.createElement('div');
            typingDiv.id = 'typing-indicator';
            typingDiv.className = 'message assistant';
            typingDiv.innerHTML = `
                <div class="message-content">
                    <div class="typing-animation">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            `;

            messagesContainer.appendChild(typingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 隐藏输入指示器
        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }

            // 清理等待状态
            isWaitingForResponse = false;
            if (typingTimeout) {
                clearTimeout(typingTimeout);
                typingTimeout = null;
            }
        }

        // WebSocket连接管理
        let ws = null;
        let wsConnected = false;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;
        let typingTimeout = null;
        let isWaitingForResponse = false;

        // 建立WebSocket连接
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/chat?session_id=${currentSessionId}`;

            try {
                ws = new WebSocket(wsUrl);

                ws.onopen = function(event) {
                    console.log('WebSocket连接已建立');
                    wsConnected = true;
                    reconnectAttempts = 0;
                };

                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleWebSocketMessage(data);
                    } catch (error) {
                        console.error('解析WebSocket消息失败:', error);
                    }
                };

                ws.onclose = function(event) {
                    console.log('WebSocket连接已关闭');
                    wsConnected = false;

                    // 尝试重连
                    if (reconnectAttempts < maxReconnectAttempts) {
                        reconnectAttempts++;
                        setTimeout(() => {
                            console.log(`尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})`);
                            connectWebSocket();
                        }, 2000 * reconnectAttempts);
                    }
                };

                ws.onerror = function(error) {
                    console.error('WebSocket错误:', error);
                    wsConnected = false;
                };

            } catch (error) {
                console.error('创建WebSocket连接失败:', error);
                wsConnected = false;
            }
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(data) {
            hideTypingIndicator();

            switch (data.type) {
                case 'connected':
                    console.log('WebSocket连接确认:', data.data);
                    break;

                case 'user_message':
                    // 用户消息确认，通常不需要处理
                    break;

                case 'assistant_message':
                    // AI助手响应
                    if (data.data && data.data.content) {
                        addMessage('assistant', data.data.content);

                        // 更新会话标题
                        if (currentSessionTitle === '新对话' && chatMessages.length <= 2) {
                            const lastUserMessage = chatMessages.find(m => m.sender === 'user');
                            if (lastUserMessage) {
                                currentSessionTitle = lastUserMessage.content.substring(0, 20) +
                                    (lastUserMessage.content.length > 20 ? '...' : '');
                            }
                        }
                    }
                    break;

                case 'error':
                    console.error('AI处理错误:', data.message);
                    addMessage('assistant', '抱歉，我遇到了一些问题，请稍后再试。');
                    break;

                case 'pong':
                    // 心跳响应
                    break;

                case 'metric_update':
                    // 处理系统指标更新
                    handleMetricUpdate(data.data);
                    break;

                default:
                    console.log('未知消息类型:', data.type);
            }
        }

        // 处理系统指标更新
        function handleMetricUpdate(metricData) {
            if (!metricData) return;

            console.log('收到系统指标更新:', metricData);

            // 这里可以添加指标显示逻辑，比如更新仪表板
            // 暂时只记录日志，避免控制台错误
            try {
                // 如果有指标显示区域，可以在这里更新
                const metricsPanel = document.getElementById('metrics-panel');
                if (metricsPanel) {
                    updateMetricsDisplay(metricData);
                }
            } catch (error) {
                console.warn('更新指标显示失败:', error);
            }
        }

        // 更新指标显示
        function updateMetricsDisplay(metricData) {
            // 实现指标显示逻辑
            const { host_name, metric_type, value, unit, timestamp } = metricData;
            console.log(`主机 ${host_name} 的 ${metric_type} 指标: ${value}${unit || ''}`);
        }

        // 发送WebSocket消息
        function sendWebSocketMessage(message) {
            if (ws && wsConnected) {
                const data = {
                    type: 'message',
                    content: message
                };
                ws.send(JSON.stringify(data));
                return true;
            }
            return false;
        }

        // 开始新对话
        function startNewChat() {
            // 清空当前对话
            const messagesContainer = document.getElementById('chat-messages');
            if (messagesContainer) {
                messagesContainer.innerHTML = '';
            }

            // 重置会话
            currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            currentSessionTitle = '新对话';
            chatMessages = [];

            // 显示欢迎界面
            showWelcomeScreen();

            console.log('Started new chat with session:', currentSessionId);
        }

        // 切换历史面板显示
        function toggleHistoryPanel() {
            const panel = document.getElementById('chat-history-panel');
            panel.classList.toggle('open');

            // 如果面板打开，加载历史记录
            if (panel.classList.contains('open')) {
                loadChatHistory();
            }
        }

        // 加载聊天历史到面板
        function loadChatHistory() {
            const sessions = JSON.parse(localStorage.getItem('chatSessions') || '[]');
            const historyList = document.getElementById('history-list');

            if (sessions.length === 0) {
                historyList.innerHTML = '<div class="no-history">暂无聊天历史</div>';
                return;
            }

            historyList.innerHTML = sessions.map(session => `
                <div class="history-item" onclick="loadSession('${session.id}')">
                    <div class="history-item-title">${session.title}</div>
                    <div class="history-item-time">${new Date(session.updatedAt).toLocaleString()}</div>
                </div>
            `).join('');
        }

        // 聊天历史功能（保持兼容性）
        function showChatHistory() {
            toggleHistoryPanel();
        }

        // 保存当前会话到历史
        function saveCurrentSession() {
            if (!currentSessionId || chatMessages.length === 0) return;

            const sessions = JSON.parse(localStorage.getItem('chatSessions') || '[]');
            const sessionIndex = sessions.findIndex(s => s.id === currentSessionId);

            const sessionData = {
                id: currentSessionId,
                title: currentSessionTitle,
                messages: chatMessages,
                updatedAt: new Date().toISOString()
            };

            if (sessionIndex >= 0) {
                sessions[sessionIndex] = sessionData;
            } else {
                sessions.unshift(sessionData);
            }

            // 限制历史记录数量
            if (sessions.length > 50) {
                sessions.splice(50);
            }

            localStorage.setItem('chatSessions', JSON.stringify(sessions));
        }


    </script>

    <!-- 添加输入指示器的CSS样式 -->
    <style>
        .typing-animation {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .typing-animation span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #6c757d;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-animation span:nth-child(1) {
            animation-delay: -0.32s;
        }

        .typing-animation span:nth-child(2) {
            animation-delay: -0.16s;
        }

        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin: 8px 0;
            overflow-x: auto;
        }

        .code-block pre {
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }

        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
    </style>













        function toggleModelSelector() {
            const dropdown = document.getElementById('model-dropdown');
            if (dropdown) {
                dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
            }
        }

        async function switchModel(modelName) {
            try {
                const response = await fetch('/api/v1/models/switch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ model_name: modelName })
                });

                const result = await response.json();

                if (result.success) {
                    currentModel = result.data;
                    updateModelSelector();

                    // 更新下拉菜单中的当前状态
                    availableModels.forEach(model => {
                        model.is_current = model.name === modelName;
                    });
                    updateModelDropdown();

                    // 隐藏下拉菜单
                    toggleModelSelector();

                    console.log('Model switched to:', currentModel.display_name);
                } else {
                    console.error('Failed to switch model:', result.error);
                    alert('切换模型失败: ' + result.error);
                }
            } catch (error) {
                console.error('Error switching model:', error);
                alert('切换模型时发生错误');
            }
        }

        // 点击其他地方关闭下拉菜单
        document.addEventListener('click', function(event) {
            const modelSelector = document.querySelector('.model-selector');
            const dropdown = document.getElementById('model-dropdown');

            if (dropdown && !modelSelector.contains(event.target)) {
                dropdown.style.display = 'none';
            }
        });

    </script>
</body>
</html>
