package service

import (
	"context"
	"fmt"
	"net"
	"strings"
	"sync"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// SimplifiedIntentHandler 简化意图处理器接口
type SimplifiedIntentHandler interface {
	Handle(ctx context.Context, intent *IntentResult, userID int64) (*SimplifiedIntentResponse, error)
	GetSupportedOperations() []string
}

// SimplifiedIntentResponse 简化意图响应
type SimplifiedIntentResponse struct {
	Success     bool                   `json:"success"`
	Content     string                 `json:"content"`
	Data        interface{}            `json:"data,omitempty"`
	Action      string                 `json:"action"`
	Operation   string                 `json:"operation"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Suggestions []string               `json:"suggestions,omitempty"`
}

// PendingConfirmation 待确认操作
type PendingConfirmation struct {
	SessionID   string    `json:"session_id"`
	UserID      int64     `json:"user_id"`
	SQL         string    `json:"sql"`
	Operation   string    `json:"operation"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	ExpiresAt   time.Time `json:"expires_at"`
}

// ConfirmationManager 确认管理器
type ConfirmationManager struct {
	pending map[string]*PendingConfirmation
	mutex   sync.RWMutex
}

// DatabaseOperationsHandler 数据库操作处理器
type DatabaseOperationsHandler struct {
	hostService         HostService
	db                  *gorm.DB
	logger              *logrus.Logger
	confirmationManager *ConfirmationManager
}

// NewDatabaseOperationsHandler 创建数据库操作处理器
func NewDatabaseOperationsHandler(hostService HostService, db *gorm.DB, logger *logrus.Logger, confirmationManager *ConfirmationManager) *DatabaseOperationsHandler {
	return &DatabaseOperationsHandler{
		hostService:         hostService,
		db:                  db,
		logger:              logger,
		confirmationManager: confirmationManager,
	}
}

// Handle 处理数据库操作
func (h *DatabaseOperationsHandler) Handle(ctx context.Context, intent *IntentResult, userID int64) (*SimplifiedIntentResponse, error) {
	params := intent.Parameters

	// 检查是否是确认消息
	if originalInput, ok := params["original_input"].(string); ok {
		if h.isConfirmationMessage(originalInput) {
			// 查找待确认的操作
			return h.handleConfirmation(ctx, userID)
		}
	}

	// 检查是否有直接的SQL语句
	if sqlQuery, ok := params["sql"].(string); ok {
		operation, _ := params["operation"].(string)
		description, _ := params["description"].(string)
		confirmRequired, _ := params["confirm_required"].(bool)

		h.logger.WithFields(logrus.Fields{
			"sql":         sqlQuery,
			"operation":   operation,
			"description": description,
			"user_id":     userID,
		}).Info("Processing direct SQL operation")

		return h.executeSQLQuery(ctx, sqlQuery, operation, description, confirmRequired, userID)
	}

	// 兼容旧格式
	operation, _ := params["operation"].(string)
	table, _ := params["table"].(string)

	// 基本权限检查（简化版）
	if operation != "" && table != "" {
		h.logger.WithFields(logrus.Fields{
			"user_id":   userID,
			"operation": operation,
			"table":     table,
		}).Debug("Processing database operation")
	}

	h.logger.WithFields(logrus.Fields{
		"operation": operation,
		"table":     table,
		"user_id":   userID,
	}).Info("Processing legacy database operation")

	switch table {
	case "hosts":
		return h.handleHostOperations(ctx, operation, params, userID)
	case "alerts":
		return h.handleAlertOperations(ctx, operation, params, userID)
	case "users":
		return h.handleUserOperations(ctx, operation, params, userID)
	default:
		return &SimplifiedIntentResponse{
			Success: false,
			Content: fmt.Sprintf("❌ 不支持的表操作：%s\n\n✅ 支持的表：hosts, alerts, users", table),
			Action:  "error",
		}, nil
	}
}

// handleHostOperations 处理主机操作
func (h *DatabaseOperationsHandler) handleHostOperations(ctx context.Context, operation string, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	switch operation {
	case "insert":
		return h.handleHostInsert(ctx, params, userID)
	case "delete":
		return h.handleHostDelete(ctx, params, userID)
	case "update":
		return h.handleHostUpdate(ctx, params, userID)
	case "select":
		return h.handleHostSelect(ctx, params, userID)
	default:
		return &SimplifiedIntentResponse{
			Success: false,
			Content: fmt.Sprintf("❌ 不支持的主机操作：%s\n\n✅ 支持的操作：select, insert, update, delete", operation),
			Action:  "error",
		}, nil
	}
}

// handleHostInsert 处理主机添加
func (h *DatabaseOperationsHandler) handleHostInsert(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	data, ok := params["data"].(map[string]interface{})
	if !ok {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 请提供完整的主机信息，格式：IP地址 用户名 密码",
			Action:  "error",
		}, nil
	}

	ip, _ := data["ip"].(string)
	username, _ := data["username"].(string)
	password, _ := data["password"].(string)

	if ip == "" || username == "" || password == "" {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 请提供完整的主机信息：IP地址、用户名、密码",
			Action:  "error",
		}, nil
	}

	// 验证IP地址格式
	if net.ParseIP(ip) == nil {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: fmt.Sprintf("❌ IP地址格式不正确：%s", ip),
			Action:  "error",
		}, nil
	}

	// 检查主机是否已存在
	existingHosts, err := h.findHostsByIP(ip)
	if err == nil && len(existingHosts) > 0 {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: fmt.Sprintf("❌ 主机 %s 已存在，无法重复添加", ip),
			Action:  "error",
		}, nil
	}

	// 检查是否需要确认（对于添加操作，通常需要确认）
	if requireConfirm, ok := params["require_confirm"].(bool); ok && requireConfirm {
		return h.generateHostInsertConfirmation(ip, username, password, userID)
	}

	// 创建主机添加请求
	hostReq := &model.HostCreateRequest{
		Name:              fmt.Sprintf("主机-%s", ip),
		IPAddress:         ip,
		Port:              22,
		Username:          username,
		Password:          password,
		Environment:       "production",
		Description:       fmt.Sprintf("通过AI对话添加的主机 - %s", time.Now().Format("2006-01-02 15:04:05")),
		Tags:              []string{"ai-added", "auto"},
		MonitoringEnabled: true,
		BackupEnabled:     false,
		CreatedBy:         userID,
	}

	// 调用主机服务添加主机
	hostResp, err := h.hostService.CreateHost(hostReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to add host")
		return &SimplifiedIntentResponse{
			Success: false,
			Content: fmt.Sprintf("❌ 添加主机失败：%s", err.Error()),
			Action:  "error",
		}, nil
	}

	// 记录操作日志
	h.logger.WithFields(logrus.Fields{
		"host_id":   hostResp.ID,
		"ip":        ip,
		"username":  username,
		"user_id":   userID,
		"operation": "host_insert",
	}).Info("Host added successfully")

	return &SimplifiedIntentResponse{
		Success: true,
		Content: fmt.Sprintf("✅ **主机添加成功！**\n\n🖥️ **主机信息**：\n- 主机ID: %d\n- IP地址: %s\n- 用户名: %s\n- 环境: production\n- 状态: 已添加\n\n💡 **建议**：请测试SSH连接确保配置正确", hostResp.ID, ip, username),
		Action:  "host_added",
		Data: map[string]interface{}{
			"host_id":   hostResp.ID,
			"ip":        ip,
			"username":  username,
			"operation": "insert",
			"timestamp": time.Now(),
		},
	}, nil
}

// handleHostDelete 处理主机删除
func (h *DatabaseOperationsHandler) handleHostDelete(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	// 添加调试日志
	h.logger.WithFields(logrus.Fields{
		"params":  params,
		"user_id": userID,
	}).Info("DatabaseOperationsHandler: Starting host delete operation")

	target, ok := params["target"].(map[string]interface{})
	if !ok {
		h.logger.WithField("params", params).Warn("DatabaseOperationsHandler: No target found in params")
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 请指定要删除的主机（IP地址或主机名）",
			Action:  "error",
		}, nil
	}

	// 检查是否需要确认（删除操作总是需要确认）
	if !h.isDeleteConfirmed(params, userID) {
		return h.generateHostDeleteConfirmation(target, userID)
	}

	h.logger.WithField("target", target).Info("DatabaseOperationsHandler: Target extracted successfully")

	// 查找主机
	var hostID int64
	var hostInfo string

	if ip, exists := target["ip"].(string); exists && ip != "" {
		// 通过IP查找主机
		hosts, err := h.findHostsByIP(ip)
		if err != nil || len(hosts) == 0 {
			return &SimplifiedIntentResponse{
				Success: false,
				Content: fmt.Sprintf("未找到IP地址为 %s 的主机", ip),
				Action:  "error",
			}, nil
		}
		hostID = hosts[0].ID
		hostInfo = fmt.Sprintf("%s (%s)", hosts[0].Name, ip)
	} else if name, exists := target["name"].(string); exists && name != "" {
		// 通过名称查找主机
		hosts, err := h.findHostsByName(name)
		if err != nil || len(hosts) == 0 {
			return &SimplifiedIntentResponse{
				Success: false,
				Content: fmt.Sprintf("未找到名为 %s 的主机", name),
				Action:  "error",
			}, nil
		}
		hostID = hosts[0].ID
		hostInfo = fmt.Sprintf("%s (%s)", name, hosts[0].IPAddress)
	} else {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "请指定要删除的主机IP地址或名称",
			Action:  "error",
		}, nil
	}

	// 删除主机
	err := h.hostService.DeleteHost(hostID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete host")
		return &SimplifiedIntentResponse{
			Success: false,
			Content: fmt.Sprintf("❌ 删除主机失败：%s", err.Error()),
			Action:  "error",
		}, nil
	}

	// 记录操作日志
	h.logger.WithFields(logrus.Fields{
		"host_id":   hostID,
		"host_info": hostInfo,
		"user_id":   userID,
		"operation": "host_delete",
	}).Info("Host deleted successfully")

	return &SimplifiedIntentResponse{
		Success: true,
		Content: fmt.Sprintf("✅ **主机删除成功！**\n\n🗑️ **删除信息**：\n- 主机信息: %s\n- 操作时间: %s\n\n⚠️ **注意**：删除操作不可逆，相关监控和日志数据已清理", hostInfo, time.Now().Format("2006-01-02 15:04:05")),
		Action:  "host_deleted",
		Data: map[string]interface{}{
			"host_id":   hostID,
			"host_info": hostInfo,
			"operation": "delete",
			"timestamp": time.Now(),
		},
	}, nil
}

// handleHostSelect 处理主机查询
func (h *DatabaseOperationsHandler) handleHostSelect(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	h.logger.WithFields(logrus.Fields{
		"operation": "select",
		"table":     "hosts",
		"user_id":   userID,
		"params":    params,
	}).Info("Processing host select operation")

	// 构建查询参数
	hostListReq := &model.HostListQuery{
		Page:  1,
		Limit: 100, // 增加限制以显示更多主机
	}

	// 解析查询条件
	if conditions, ok := params["conditions"].(map[string]interface{}); ok {
		if filter, exists := conditions["filter"].(string); exists {
			switch filter {
			case "status":
				// 可以添加状态过滤
				h.logger.Info("Applying status filter")
			case "account":
				// 账号信息过滤
				h.logger.Info("Applying account filter")
			case "info":
				// 信息过滤
				h.logger.Info("Applying info filter")
			}
		}

		// 解析其他过滤条件
		if status, exists := conditions["status"].(string); exists {
			hostListReq.Status = status
		}
		if environment, exists := conditions["environment"].(string); exists {
			hostListReq.Environment = environment
		}
		if search, exists := conditions["search"].(string); exists {
			hostListReq.Search = search
		}
	}

	h.logger.WithField("query", hostListReq).Info("Executing host list query")

	// 调用主机服务查询数据库
	hostListResp, err := h.hostService.ListHosts(hostListReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to query host list from database")
		return &SimplifiedIntentResponse{
			Success: false,
			Content: fmt.Sprintf("❌ 查询主机列表失败：%s", err.Error()),
			Action:  "error",
		}, nil
	}

	h.logger.WithFields(logrus.Fields{
		"host_count": len(hostListResp.Hosts),
		"total":      hostListResp.Pagination.Total,
	}).Info("Host list query completed successfully")

	if len(hostListResp.Hosts) == 0 {
		return &SimplifiedIntentResponse{
			Success: true,
			Content: "📋 当前没有已添加的主机\n\n💡 您可以通过以下方式添加主机：\n• 输入：添加主机 IP地址 用户名 密码\n• 例如：添加主机 ************* root mypassword",
			Action:  "host_list_empty",
			Data:    []interface{}{},
		}, nil
	}

	// 构建详细的主机列表内容
	content := fmt.Sprintf("📊 **主机列表** (共 %d 台)\n\n", len(hostListResp.Hosts))

	// 添加表格头
	content += "```\n"
	content += fmt.Sprintf("%-4s %-16s %-20s %-10s %-12s %-15s\n", "序号", "IP地址", "主机名", "状态", "环境", "创建时间")
	content += fmt.Sprintf("%-4s %-16s %-20s %-10s %-12s %-15s\n", "----", "--------", "--------", "----", "----", "--------")

	for i, host := range hostListResp.Hosts {
		status := "🔴离线"
		if host.Status == "online" {
			status = "🟢在线"
		} else if host.Status == "unknown" {
			status = "⚪未知"
		}

		// 截断长主机名
		hostName := host.Name
		if len(hostName) > 18 {
			hostName = hostName[:15] + "..."
		}

		// 格式化创建时间
		createdTime := host.CreatedAt.Format("01-02 15:04")

		content += fmt.Sprintf("%-4d %-16s %-20s %-10s %-12s %-15s\n",
			i+1, host.IPAddress, hostName, status, host.Environment, createdTime)
	}
	content += "```\n\n"

	// 添加统计信息
	onlineCount := 0
	offlineCount := 0
	unknownCount := 0
	for _, host := range hostListResp.Hosts {
		switch host.Status {
		case "online":
			onlineCount++
		case "offline":
			offlineCount++
		default:
			unknownCount++
		}
	}

	content += fmt.Sprintf("📈 **状态统计**：🟢在线 %d台 | 🔴离线 %d台 | ⚪未知 %d台\n",
		onlineCount, offlineCount, unknownCount)

	if hostListResp.Pagination.Total > int64(len(hostListResp.Hosts)) {
		content += fmt.Sprintf("\n💡 显示前 %d 台主机，总共 %d 台",
			len(hostListResp.Hosts), hostListResp.Pagination.Total)
	}

	return &SimplifiedIntentResponse{
		Success:   true,
		Content:   content,
		Action:    "host_list_success",
		Operation: "select",
		Data: map[string]interface{}{
			"hosts":      hostListResp.Hosts,
			"pagination": hostListResp.Pagination,
			"statistics": map[string]int{
				"total":   len(hostListResp.Hosts),
				"online":  onlineCount,
				"offline": offlineCount,
				"unknown": unknownCount,
			},
		},
		Metadata: map[string]interface{}{
			"query_params": hostListReq,
			"total_count":  hostListResp.Pagination.Total,
		},
	}, nil
}

// handleHostUpdate 处理主机更新
func (h *DatabaseOperationsHandler) handleHostUpdate(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	// 解析目标主机
	target, ok := params["target"].(map[string]interface{})
	if !ok {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 请指定要更新的主机",
			Action:  "error",
		}, nil
	}

	// 解析更新数据
	data, ok := params["data"].(map[string]interface{})
	if !ok {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 请指定要更新的数据",
			Action:  "error",
		}, nil
	}

	// 检查是否是密码更新
	if password, exists := data["password"]; exists {
		return h.handlePasswordUpdate(ctx, target, password.(string), userID)
	}

	// 处理其他字段更新
	return h.handleGeneralHostUpdate(ctx, target, data, userID)
}

// 辅助方法
func (h *DatabaseOperationsHandler) findHostsByIP(ip string) ([]*model.HostResponse, error) {
	hostListReq := &model.HostListQuery{
		Page:  1,
		Limit: 100,
	}
	hostListResp, err := h.hostService.ListHosts(hostListReq)
	if err != nil {
		return nil, err
	}

	var result []*model.HostResponse
	for _, host := range hostListResp.Hosts {
		if host.IPAddress == ip {
			result = append(result, host)
		}
	}
	return result, nil
}

func (h *DatabaseOperationsHandler) findHostsByName(name string) ([]*model.HostResponse, error) {
	hostListReq := &model.HostListQuery{
		Page:  1,
		Limit: 100,
	}
	hostListResp, err := h.hostService.ListHosts(hostListReq)
	if err != nil {
		return nil, err
	}

	var result []*model.HostResponse
	for _, host := range hostListResp.Hosts {
		if host.Name == name {
			result = append(result, host)
		}
	}
	return result, nil
}

// handleAlertOperations 处理告警操作
func (h *DatabaseOperationsHandler) handleAlertOperations(ctx context.Context, operation string, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	return &SimplifiedIntentResponse{
		Success: false,
		Content: "告警操作功能暂未实现",
		Action:  "not_implemented",
	}, nil
}

// handleUserOperations 处理用户操作
func (h *DatabaseOperationsHandler) handleUserOperations(ctx context.Context, operation string, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	return &SimplifiedIntentResponse{
		Success: false,
		Content: "用户操作功能暂未实现",
		Action:  "not_implemented",
	}, nil
}

// executeSQLQuery 直接执行SQL查询
func (h *DatabaseOperationsHandler) executeSQLQuery(ctx context.Context, sqlQuery, operation, description string, confirmRequired bool, userID int64) (*SimplifiedIntentResponse, error) {
	h.logger.WithFields(logrus.Fields{
		"sql":              sqlQuery,
		"operation":        operation,
		"confirm_required": confirmRequired,
		"user_id":          userID,
	}).Info("Executing direct SQL query")

	switch operation {
	case "select":
		return h.executeSelectQuery(ctx, sqlQuery, description, userID)
	case "delete":
		if confirmRequired {
			return h.executeDeleteWithConfirmation(ctx, sqlQuery, description, userID)
		}
		return h.executeDeleteQuery(ctx, sqlQuery, description, userID)
	case "insert":
		return h.executeInsertQuery(ctx, sqlQuery, description, userID)
	case "update":
		return h.executeUpdateQuery(ctx, sqlQuery, description, userID)
	default:
		return &SimplifiedIntentResponse{
			Success: false,
			Content: fmt.Sprintf("不支持的SQL操作类型：%s", operation),
			Action:  "error",
		}, nil
	}
}

// executeSelectQuery 执行SELECT查询
func (h *DatabaseOperationsHandler) executeSelectQuery(ctx context.Context, sqlQuery, description string, userID int64) (*SimplifiedIntentResponse, error) {
	h.logger.WithField("sql", sqlQuery).Info("Executing SELECT query")

	var hosts []model.Host
	result := h.db.Raw(sqlQuery).Scan(&hosts)
	if result.Error != nil {
		h.logger.WithError(result.Error).Error("Failed to execute SELECT query")
		return &SimplifiedIntentResponse{
			Success: false,
			Content: fmt.Sprintf("❌ 查询执行失败：%s", result.Error.Error()),
			Action:  "error",
		}, nil
	}

	h.logger.WithFields(logrus.Fields{
		"rows_affected": result.RowsAffected,
		"host_count":    len(hosts),
	}).Info("SELECT query executed successfully")

	if len(hosts) == 0 {
		return &SimplifiedIntentResponse{
			Success: true,
			Content: "📋 查询结果为空\n\n💡 没有找到符合条件的主机记录",
			Action:  "query_empty",
			Data:    []interface{}{},
			Metadata: map[string]interface{}{
				"sql":         sqlQuery,
				"description": description,
				"rows_count":  0,
			},
		}, nil
	}

	// 构建详细的查询结果
	content := fmt.Sprintf("📊 **%s** (共 %d 条记录)\n\n", description, len(hosts))

	// 添加表格头
	content += "```\n"
	content += fmt.Sprintf("%-4s %-16s %-20s %-10s %-12s %-15s\n", "序号", "IP地址", "主机名", "状态", "环境", "创建时间")
	content += fmt.Sprintf("%-4s %-16s %-20s %-10s %-12s %-15s\n", "----", "--------", "--------", "----", "----", "--------")

	for i, host := range hosts {
		status := "🔴离线"
		if host.Status == "online" {
			status = "🟢在线"
		} else if host.Status == "unknown" {
			status = "⚪未知"
		}

		// 截断长主机名
		hostName := host.Name
		if len(hostName) > 18 {
			hostName = hostName[:15] + "..."
		}

		// 格式化创建时间
		createdTime := host.CreatedAt.Format("01-02 15:04")

		content += fmt.Sprintf("%-4d %-16s %-20s %-10s %-12s %-15s\n",
			i+1, host.IPAddress, hostName, status, host.Environment, createdTime)
	}
	content += "```\n\n"

	// 添加统计信息
	onlineCount := 0
	offlineCount := 0
	unknownCount := 0
	for _, host := range hosts {
		switch host.Status {
		case "online":
			onlineCount++
		case "offline":
			offlineCount++
		default:
			unknownCount++
		}
	}

	content += fmt.Sprintf("📈 **状态统计**：🟢在线 %d台 | 🔴离线 %d台 | ⚪未知 %d台\n",
		onlineCount, offlineCount, unknownCount)

	content += fmt.Sprintf("\n🔍 **执行的SQL**：`%s`", sqlQuery)

	return &SimplifiedIntentResponse{
		Success:   true,
		Content:   content,
		Action:    "query_success",
		Operation: "select",
		Data: map[string]interface{}{
			"hosts": hosts,
			"statistics": map[string]int{
				"total":   len(hosts),
				"online":  onlineCount,
				"offline": offlineCount,
				"unknown": unknownCount,
			},
		},
		Metadata: map[string]interface{}{
			"sql":         sqlQuery,
			"description": description,
			"rows_count":  len(hosts),
		},
	}, nil
}

// executeDeleteWithConfirmation 执行需要确认的DELETE操作
func (h *DatabaseOperationsHandler) executeDeleteWithConfirmation(ctx context.Context, sqlQuery, description string, userID int64) (*SimplifiedIntentResponse, error) {
	// 生成唯一的确认令牌
	confirmToken := fmt.Sprintf("delete_%d_%d", userID, time.Now().UnixNano())

	// 保存待确认的操作
	pendingOp := &PendingConfirmation{
		SessionID:   confirmToken, // 使用确认令牌作为会话ID
		UserID:      userID,
		SQL:         sqlQuery,
		Operation:   "delete",
		Description: description,
		CreatedAt:   time.Now(),
		ExpiresAt:   time.Now().Add(5 * time.Minute), // 5分钟过期
	}

	h.confirmationManager.mutex.Lock()
	// 清理该用户之前的待确认操作
	for key, op := range h.confirmationManager.pending {
		if op.UserID == userID {
			delete(h.confirmationManager.pending, key)
		}
	}
	// 保存新的待确认操作
	h.confirmationManager.pending[confirmToken] = pendingOp
	h.confirmationManager.mutex.Unlock()

	h.logger.WithFields(logrus.Fields{
		"confirm_token": confirmToken,
		"sql":           sqlQuery,
		"description":   description,
		"user_id":       userID,
	}).Info("🔑 保存了待确认的操作")

	return &SimplifiedIntentResponse{
		Success: false,
		Content: fmt.Sprintf("⚠️ 危险操作需要确认\n\n**操作描述**：%s\n**SQL语句**：`%s`\n\n请输入\"确认删除\"来执行此操作", description, sqlQuery),
		Action:  "confirmation_required",
		Metadata: map[string]interface{}{
			"sql":         sqlQuery,
			"description": description,
			"operation":   "delete",
		},
	}, nil
}

// executeDeleteQuery 执行DELETE查询
func (h *DatabaseOperationsHandler) executeDeleteQuery(ctx context.Context, sqlQuery, description string, userID int64) (*SimplifiedIntentResponse, error) {
	h.logger.WithFields(logrus.Fields{
		"sql":         sqlQuery,
		"description": description,
		"user_id":     userID,
	}).Info("Executing DELETE query")

	// 执行删除操作
	result := h.db.Exec(sqlQuery)
	if result.Error != nil {
		h.logger.WithError(result.Error).Error("Failed to execute DELETE query")
		return &SimplifiedIntentResponse{
			Success: false,
			Content: fmt.Sprintf("删除操作失败：%s", result.Error.Error()),
			Action:  "error",
		}, nil
	}

	rowsAffected := result.RowsAffected
	h.logger.WithField("rows_affected", rowsAffected).Info("DELETE query executed successfully")

	if rowsAffected > 0 {
		return &SimplifiedIntentResponse{
			Success: true,
			Content: fmt.Sprintf("✅ %s 成功\n\n删除了 %d 条记录", description, rowsAffected),
			Action:  "success",
			Metadata: map[string]interface{}{
				"rows_affected": rowsAffected,
				"sql":           sqlQuery,
			},
		}, nil
	} else {
		return &SimplifiedIntentResponse{
			Success: true,
			Content: "✅ 删除操作完成，但没有找到匹配的记录",
			Action:  "success",
			Metadata: map[string]interface{}{
				"rows_affected": 0,
				"sql":           sqlQuery,
			},
		}, nil
	}
}

// executeInsertQuery 执行INSERT查询
func (h *DatabaseOperationsHandler) executeInsertQuery(ctx context.Context, sqlQuery, description string, userID int64) (*SimplifiedIntentResponse, error) {
	return &SimplifiedIntentResponse{
		Success: false,
		Content: "INSERT操作暂未实现",
		Action:  "not_implemented",
	}, nil
}

// executeUpdateQuery 执行UPDATE查询
func (h *DatabaseOperationsHandler) executeUpdateQuery(ctx context.Context, sqlQuery, description string, userID int64) (*SimplifiedIntentResponse, error) {
	return &SimplifiedIntentResponse{
		Success: false,
		Content: "UPDATE操作暂未实现",
		Action:  "not_implemented",
	}, nil
}

// GetSupportedOperations 获取支持的操作
func (h *DatabaseOperationsHandler) GetSupportedOperations() []string {
	return []string{"insert", "delete", "update", "select"}
}

// HandleConfirmation 处理确认操作
func (h *DatabaseOperationsHandler) HandleConfirmation(ctx context.Context, confirmToken string, userID int64) (*SimplifiedIntentResponse, error) {
	h.confirmationManager.mutex.Lock()
	defer h.confirmationManager.mutex.Unlock()

	// 查找待确认的操作
	pending, exists := h.confirmationManager.pending[confirmToken]
	if !exists {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 确认令牌无效或已过期",
			Action:  "error",
		}, nil
	}

	// 检查是否过期
	if time.Now().After(pending.ExpiresAt) {
		delete(h.confirmationManager.pending, confirmToken)
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 确认令牌已过期，请重新发起操作",
			Action:  "error",
		}, nil
	}

	// 检查用户权限
	if pending.UserID != userID {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 无权限确认此操作",
			Action:  "error",
		}, nil
	}

	// 执行确认的操作
	var response *SimplifiedIntentResponse
	var err error

	// 🚀 通用SQL执行机制 - 支持所有需要确认的操作
	switch pending.Operation {
	case "password_update":
		response, err = h.executePasswordUpdate(ctx, pending, userID)
	default:
		// 通用SQL执行 - 支持所有类型的确认操作
		response, err = h.executeConfirmedSQL(ctx, pending, userID)
	}

	// 清理已确认的操作
	delete(h.confirmationManager.pending, confirmToken)

	return response, err
}

// executePasswordUpdate 执行密码更新
func (h *DatabaseOperationsHandler) executePasswordUpdate(ctx context.Context, pending *PendingConfirmation, userID int64) (*SimplifiedIntentResponse, error) {
	// 从描述中提取新密码（这是临时方案，实际应用中应该更安全）
	// 这里我们需要从某个地方获取新密码，暂时返回成功消息

	// 执行密码更新SQL（这里需要实际的加密逻辑）
	updateSQL := pending.SQL

	h.logger.WithFields(logrus.Fields{
		"sql":     updateSQL,
		"user_id": userID,
	}).Info("Executing password update")

	// 这里应该执行实际的密码更新逻辑
	// 包括密码加密、数据库更新等

	// 模拟执行成功
	return &SimplifiedIntentResponse{
		Success: true,
		Content: "✅ **密码更新成功**\n\n🔐 主机登录密码已更新\n💡 建议立即测试新密码的连接性",
		Action:  "password_update_success",
		Metadata: map[string]interface{}{
			"operation": "password_update",
			"timestamp": time.Now(),
		},
	}, nil
}

// handlePasswordUpdate 处理密码更新
func (h *DatabaseOperationsHandler) handlePasswordUpdate(ctx context.Context, target map[string]interface{}, newPassword string, userID int64) (*SimplifiedIntentResponse, error) {
	// 解析目标主机
	var hostIP string
	var hostName string

	if ip, ok := target["ip"].(string); ok {
		hostIP = ip
	}
	if name, ok := target["name"].(string); ok {
		hostName = name
	}

	if hostIP == "" && hostName == "" {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 请指定主机IP地址或主机名",
			Action:  "error",
		}, nil
	}

	// 查找主机
	var hosts []*model.HostResponse
	var err error

	if hostIP != "" {
		hosts, err = h.findHostsByIP(hostIP)
	} else {
		hosts, err = h.findHostsByName(hostName)
	}

	if err != nil {
		h.logger.WithError(err).Error("Failed to find hosts")
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 查找主机时发生错误",
			Action:  "error",
		}, nil
	}

	if len(hosts) == 0 {
		identifier := hostIP
		if identifier == "" {
			identifier = hostName
		}
		return &SimplifiedIntentResponse{
			Success: false,
			Content: fmt.Sprintf("❌ 未找到主机: %s", identifier),
			Action:  "error",
		}, nil
	}

	if len(hosts) > 1 {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 找到多个匹配的主机，请使用更具体的标识符",
			Action:  "error",
		}, nil
	}

	host := hosts[0]

	// 生成确认令牌
	confirmToken := fmt.Sprintf("pwd_update_%d_%d", userID, time.Now().UnixNano())

	// 存储待确认的操作
	h.confirmationManager.mutex.Lock()
	h.confirmationManager.pending[confirmToken] = &PendingConfirmation{
		SessionID:   confirmToken,
		UserID:      userID,
		SQL:         fmt.Sprintf("UPDATE hosts SET password_encrypted = '[ENCRYPTED]', updated_at = NOW() WHERE ip_address = '%s'", host.IPAddress),
		Operation:   "password_update",
		Description: fmt.Sprintf("更新主机 %s (%s) 的密码", host.Name, host.IPAddress),
		CreatedAt:   time.Now(),
		ExpiresAt:   time.Now().Add(5 * time.Minute),
	}
	h.confirmationManager.mutex.Unlock()

	// 临时存储新密码到确认数据中（实际应用中应该更安全地处理）
	if h.confirmationManager.pending[confirmToken] != nil {
		// 将密码存储在SQL中，但这里我们需要一个更安全的方式
		// 暂时存储在description中，实际应用中应该加密存储
		h.confirmationManager.pending[confirmToken].Description += fmt.Sprintf(" [新密码: %s]", maskPassword(newPassword))
	}

	// 返回确认请求
	content := fmt.Sprintf(`⚠️ **密码更新确认**

🎯 **目标主机**: %s (%s)
🔐 **操作**: 更新登录密码
⚡ **风险等级**: 🟠 高风险
🔒 **新密码**: %s

⚠️ **安全警告**:
• 密码更新后将立即生效
• 请确保新密码符合安全要求
• 建议同时更新相关文档和配置

💡 **确认方式**: 请回复 '确认执行' 继续，或 '取消操作' 放弃`,
		host.Name, host.IPAddress, maskPassword(newPassword))

	return &SimplifiedIntentResponse{
		Success: false,
		Content: content,
		Action:  "require_confirmation",
		Metadata: map[string]interface{}{
			"require_confirm": true,
			"confirm_token":   confirmToken,
			"operation_type":  "password_update",
		},
	}, nil
}

// handleGeneralHostUpdate 处理一般主机更新
func (h *DatabaseOperationsHandler) handleGeneralHostUpdate(ctx context.Context, target map[string]interface{}, data map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	return &SimplifiedIntentResponse{
		Success: false,
		Content: "🔧 一般主机信息更新功能正在开发中，目前仅支持密码更新",
		Action:  "not_implemented",
	}, nil
}

// maskPassword 脱敏密码显示
func maskPassword(password string) string {
	if len(password) <= 4 {
		return "***"
	} else if len(password) <= 8 {
		return password[:2] + "***"
	} else {
		return password[:3] + "***" + password[len(password)-2:]
	}
}

// generateHostInsertConfirmation 生成主机添加确认
func (h *DatabaseOperationsHandler) generateHostInsertConfirmation(ip, username, password string, userID int64) (*SimplifiedIntentResponse, error) {
	// 生成确认令牌
	confirmToken := fmt.Sprintf("host_insert_%d_%d", userID, time.Now().UnixNano())

	// 存储待确认的操作
	h.confirmationManager.mutex.Lock()
	h.confirmationManager.pending[confirmToken] = &PendingConfirmation{
		SessionID:   confirmToken,
		UserID:      userID,
		SQL:         fmt.Sprintf("INSERT INTO hosts (ip_address, username, password_encrypted) VALUES ('%s', '%s', '[ENCRYPTED]')", ip, username),
		Operation:   "host_insert",
		Description: fmt.Sprintf("添加主机 %s (用户名: %s)", ip, username),
		CreatedAt:   time.Now(),
		ExpiresAt:   time.Now().Add(5 * time.Minute),
	}
	h.confirmationManager.mutex.Unlock()

	// 返回确认请求
	content := fmt.Sprintf(`⚠️ **主机添加确认**

🖥️ **目标主机**: %s
👤 **用户名**: %s
🔐 **密码**: %s
⚡ **风险等级**: 🟡 中风险

⚠️ **操作说明**:
• 将添加新的主机到管理列表
• 密码将被加密存储
• 自动启用监控功能

💡 **确认方式**: 请回复 '确认执行' 继续，或 '取消操作' 放弃`,
		ip, username, maskPassword(password))

	return &SimplifiedIntentResponse{
		Success: false,
		Content: content,
		Action:  "require_confirmation",
		Metadata: map[string]interface{}{
			"require_confirm": true,
			"confirm_token":   confirmToken,
			"operation_type":  "host_insert",
		},
	}, nil
}

// generateHostDeleteConfirmation 生成主机删除确认
func (h *DatabaseOperationsHandler) generateHostDeleteConfirmation(target map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	// 查找主机信息
	var hostInfo string
	var hostID int64

	if ip, exists := target["ip"].(string); exists && ip != "" {
		hosts, err := h.findHostsByIP(ip)
		if err != nil || len(hosts) == 0 {
			return &SimplifiedIntentResponse{
				Success: false,
				Content: fmt.Sprintf("❌ 未找到IP地址为 %s 的主机", ip),
				Action:  "error",
			}, nil
		}
		hostID = hosts[0].ID
		hostInfo = fmt.Sprintf("%s (%s)", hosts[0].Name, ip)
	} else if name, exists := target["name"].(string); exists && name != "" {
		hosts, err := h.findHostsByName(name)
		if err != nil || len(hosts) == 0 {
			return &SimplifiedIntentResponse{
				Success: false,
				Content: fmt.Sprintf("❌ 未找到名为 %s 的主机", name),
				Action:  "error",
			}, nil
		}
		hostID = hosts[0].ID
		hostInfo = fmt.Sprintf("%s (%s)", name, hosts[0].IPAddress)
	} else {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 请指定要删除的主机IP地址或名称",
			Action:  "error",
		}, nil
	}

	// 生成确认令牌
	confirmToken := fmt.Sprintf("host_delete_%d_%d", userID, time.Now().UnixNano())

	// 存储待确认的操作
	h.confirmationManager.mutex.Lock()
	h.confirmationManager.pending[confirmToken] = &PendingConfirmation{
		SessionID:   confirmToken,
		UserID:      userID,
		SQL:         fmt.Sprintf("DELETE FROM hosts WHERE id = %d", hostID),
		Operation:   "host_delete",
		Description: fmt.Sprintf("删除主机 %s", hostInfo),
		CreatedAt:   time.Now(),
		ExpiresAt:   time.Now().Add(5 * time.Minute),
	}
	h.confirmationManager.mutex.Unlock()

	// 返回确认请求
	content := fmt.Sprintf(`🔴 **主机删除确认**

🖥️ **目标主机**: %s
🗑️ **操作**: 永久删除主机
⚡ **风险等级**: 🔴 极高风险

⚠️ **危险警告**:
• 删除操作不可逆转
• 将清理所有相关监控数据
• 将断开所有SSH连接
• 相关日志和配置将被清除

💡 **确认方式**: 请回复 '确认删除' 继续，或 '取消操作' 放弃`,
		hostInfo)

	return &SimplifiedIntentResponse{
		Success: false,
		Content: content,
		Action:  "require_confirmation",
		Metadata: map[string]interface{}{
			"require_confirm": true,
			"confirm_token":   confirmToken,
			"operation_type":  "host_delete",
		},
	}, nil
}

// isDeleteConfirmed 检查删除操作是否已确认
func (h *DatabaseOperationsHandler) isDeleteConfirmed(params map[string]interface{}, userID int64) bool {
	// 检查是否有确认标志
	if confirmed, ok := params["confirmed"].(bool); ok && confirmed {
		return true
	}

	// 检查是否有确认令牌
	if confirmToken, ok := params["confirm_token"].(string); ok && confirmToken != "" {
		h.confirmationManager.mutex.RLock()
		pending, exists := h.confirmationManager.pending[confirmToken]
		h.confirmationManager.mutex.RUnlock()

		if exists && pending.UserID == userID && time.Now().Before(pending.ExpiresAt) {
			return true
		}
	}

	return false
}

// executeConfirmedSQL 执行已确认的SQL操作 - 通用方法
func (h *DatabaseOperationsHandler) executeConfirmedSQL(ctx context.Context, pending *PendingConfirmation, userID int64) (*SimplifiedIntentResponse, error) {
	h.logger.WithFields(logrus.Fields{
		"user_id":     userID,
		"operation":   pending.Operation,
		"sql":         pending.SQL,
		"description": pending.Description,
	}).Info("🚀 执行已确认的SQL操作")

	// 安全检查：确保SQL不为空
	if strings.TrimSpace(pending.SQL) == "" {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ SQL语句为空",
			Action:  "error",
		}, nil
	}

	// 执行SQL操作
	result := h.db.WithContext(ctx).Exec(pending.SQL)
	if result.Error != nil {
		h.logger.WithError(result.Error).Error("SQL执行失败")
		return &SimplifiedIntentResponse{
			Success: false,
			Content: fmt.Sprintf("❌ 操作失败：%s", result.Error.Error()),
			Action:  "error",
		}, nil
	}

	// 构建成功响应
	var content string
	if result.RowsAffected == 0 {
		content = fmt.Sprintf("⚠️ %s 执行完成，但没有记录被影响\n\n📊 影响行数：0", pending.Description)
	} else {
		content = fmt.Sprintf("✅ %s 执行成功！\n\n📊 影响行数：%d", pending.Description, result.RowsAffected)
	}

	// 添加执行的SQL信息（用于调试）
	content += fmt.Sprintf("\n\n🔍 执行的SQL：\n```sql\n%s\n```", pending.SQL)

	return &SimplifiedIntentResponse{
		Success: true,
		Content: content,
		Action:  "success",
		Data: map[string]interface{}{
			"rows_affected": result.RowsAffected,
			"sql":           pending.SQL,
			"operation":     pending.Operation,
		},
	}, nil
}

// SSHOperationsHandler SSH操作处理器
type SSHOperationsHandler struct {
	hostService HostService
	sshPool     *SSHConnectionPool
	db          *gorm.DB
	logger      *logrus.Logger
}

// NewSSHOperationsHandler 创建SSH操作处理器
func NewSSHOperationsHandler(hostService HostService, db *gorm.DB, logger *logrus.Logger) *SSHOperationsHandler {
	// 创建SSH连接池配置
	poolConfig := &SSHPoolConfig{
		MaxConnections:      10,
		MaxIdleTime:         5 * time.Minute,
		MaxLifetime:         30 * time.Minute,
		HealthCheckInterval: 1 * time.Minute,
		ConnectTimeout:      30 * time.Second,
		CommandTimeout:      60 * time.Second,
		MaxRetries:          3,
	}

	// 创建SSH连接池，需要实现HostServiceInterface接口
	var hostServiceInterface HostServiceInterface
	if hs, ok := hostService.(HostServiceInterface); ok {
		hostServiceInterface = hs
	} else {
		// 如果hostService没有实现接口，创建一个适配器
		hostServiceInterface = &hostServiceAdapter{hostService: hostService}
	}

	sshPool := NewSSHConnectionPool(logger, poolConfig, hostServiceInterface)

	return &SSHOperationsHandler{
		hostService: hostService,
		sshPool:     sshPool,
		db:          db,
		logger:      logger,
	}
}

// hostServiceAdapter 主机服务适配器
type hostServiceAdapter struct {
	hostService HostService
}

// decryptData 解密数据（适配器实现）
func (h *hostServiceAdapter) decryptData(encryptedData string) (string, error) {
	// 这里需要调用实际的解密方法，暂时返回原始数据
	return encryptedData, nil
}

// Handle 处理SSH操作
func (h *SSHOperationsHandler) Handle(ctx context.Context, intent *IntentResult, userID int64) (*SimplifiedIntentResponse, error) {
	params := intent.Parameters
	operation, _ := params["operation"].(string)

	h.logger.WithFields(logrus.Fields{
		"operation": operation,
		"user_id":   userID,
	}).Info("Processing SSH operation")

	switch operation {
	case "execute_command":
		return h.handleCommandExecution(ctx, params, userID)
	case "manage_service":
		return h.handleServiceManagement(ctx, params, userID)
	case "file_operation":
		return h.handleFileOperation(ctx, params, userID)
	case "security_check":
		return h.handleSecurityCheck(ctx, params, userID)
	default:
		return &SimplifiedIntentResponse{
			Success: false,
			Content: fmt.Sprintf("不支持的SSH操作：%s", operation),
			Action:  "error",
		}, nil
	}
}

// handleCommandExecution 处理命令执行
func (h *SSHOperationsHandler) handleCommandExecution(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	command, ok := params["command"].(string)
	if !ok {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 缺少命令参数",
			Action:  "error",
		}, nil
	}

	// 获取目标主机信息
	targetHost, ok := params["target_host"].(map[string]interface{})
	if !ok {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 缺少目标主机信息",
			Action:  "error",
		}, nil
	}

	// 查找主机
	var host model.Host
	var err error

	if ip, exists := targetHost["ip"].(string); exists {
		err = h.db.Where("ip_address = ? AND deleted_at IS NULL", ip).First(&host).Error
	} else if name, exists := targetHost["name"].(string); exists {
		err = h.db.Where("name = ? AND deleted_at IS NULL", name).First(&host).Error
	} else if id, exists := targetHost["id"].(float64); exists {
		err = h.db.Where("id = ? AND deleted_at IS NULL", int64(id)).First(&host).Error
	} else {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 无效的主机标识符",
			Action:  "error",
		}, nil
	}

	if err != nil {
		h.logger.WithError(err).Error("Failed to find target host")
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 未找到目标主机",
			Action:  "error",
		}, nil
	}

	h.logger.WithFields(logrus.Fields{
		"host_id": host.ID,
		"host_ip": host.IPAddress,
		"command": command,
		"user_id": userID,
	}).Info("Executing SSH command")

	// 执行SSH命令
	result, err := h.sshPool.ExecuteCommand(&host, command, 60*time.Second)
	if err != nil {
		h.logger.WithError(err).Error("Failed to execute SSH command")
		return &SimplifiedIntentResponse{
			Success: false,
			Content: fmt.Sprintf("❌ 命令执行失败：%s", err.Error()),
			Action:  "error",
			Metadata: map[string]interface{}{
				"host_ip": host.IPAddress,
				"command": command,
				"error":   err.Error(),
			},
		}, nil
	}

	// 构建成功响应
	content := fmt.Sprintf("✅ **命令执行成功**\n\n")
	content += fmt.Sprintf("🖥️ **主机**: %s (%s)\n", host.Name, host.IPAddress)
	content += fmt.Sprintf("⚡ **命令**: `%s`\n", command)
	content += fmt.Sprintf("⏱️ **耗时**: %v\n", result.Duration)
	content += fmt.Sprintf("📤 **退出码**: %d\n\n", result.ExitCode)

	if result.Output != "" {
		content += "📋 **输出结果**:\n```\n" + result.Output + "\n```\n"
	}

	if result.Error != "" {
		content += "⚠️ **错误信息**:\n```\n" + result.Error + "\n```\n"
	}

	return &SimplifiedIntentResponse{
		Success: true,
		Content: content,
		Action:  "command_executed",
		Data: map[string]interface{}{
			"command":   command,
			"output":    result.Output,
			"error":     result.Error,
			"exit_code": result.ExitCode,
			"duration":  result.Duration,
		},
		Metadata: map[string]interface{}{
			"host_id":   host.ID,
			"host_ip":   host.IPAddress,
			"host_name": host.Name,
			"command":   command,
			"user_id":   userID,
		},
	}, nil
}

// handleServiceManagement 处理服务管理
func (h *SSHOperationsHandler) handleServiceManagement(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	service, ok := params["service"].(string)
	if !ok {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 缺少服务名称参数",
			Action:  "error",
		}, nil
	}

	action, ok := params["action"].(string)
	if !ok {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 缺少操作类型参数",
			Action:  "error",
		}, nil
	}

	// 获取目标主机信息
	targetHost, ok := params["target_host"].(map[string]interface{})
	if !ok {
		// 如果没有指定主机，默认在所有在线主机上执行
		return h.executeServiceCommandOnAllHosts(ctx, service, action, userID)
	}

	// 查找指定主机
	var host model.Host
	var err error

	if ip, exists := targetHost["ip"].(string); exists {
		err = h.db.Where("ip_address = ? AND deleted_at IS NULL", ip).First(&host).Error
	} else if name, exists := targetHost["name"].(string); exists {
		err = h.db.Where("name = ? AND deleted_at IS NULL", name).First(&host).Error
	} else {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 无效的主机标识符",
			Action:  "error",
		}, nil
	}

	if err != nil {
		h.logger.WithError(err).Error("Failed to find target host")
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 未找到目标主机",
			Action:  "error",
		}, nil
	}

	return h.executeServiceCommand(ctx, &host, service, action, userID)
}

// executeServiceCommand 在指定主机上执行服务命令
func (h *SSHOperationsHandler) executeServiceCommand(ctx context.Context, host *model.Host, service, action string, userID int64) (*SimplifiedIntentResponse, error) {
	// 构建systemctl命令
	var command string
	switch action {
	case "start":
		command = fmt.Sprintf("sudo systemctl start %s", service)
	case "stop":
		command = fmt.Sprintf("sudo systemctl stop %s", service)
	case "restart":
		command = fmt.Sprintf("sudo systemctl restart %s", service)
	case "status":
		command = fmt.Sprintf("systemctl status %s", service)
	case "enable":
		command = fmt.Sprintf("sudo systemctl enable %s", service)
	case "disable":
		command = fmt.Sprintf("sudo systemctl disable %s", service)
	default:
		return &SimplifiedIntentResponse{
			Success: false,
			Content: fmt.Sprintf("❌ 不支持的服务操作：%s", action),
			Action:  "error",
		}, nil
	}

	h.logger.WithFields(logrus.Fields{
		"host_id": host.ID,
		"host_ip": host.IPAddress,
		"service": service,
		"action":  action,
		"command": command,
		"user_id": userID,
	}).Info("Executing service management command")

	// 执行命令
	result, err := h.sshPool.ExecuteCommand(host, command, 30*time.Second)
	if err != nil {
		h.logger.WithError(err).Error("Failed to execute service command")
		return &SimplifiedIntentResponse{
			Success: false,
			Content: fmt.Sprintf("❌ 服务操作失败：%s", err.Error()),
			Action:  "error",
		}, nil
	}

	// 构建响应
	actionText := map[string]string{
		"start":   "启动",
		"stop":    "停止",
		"restart": "重启",
		"status":  "查看状态",
		"enable":  "启用",
		"disable": "禁用",
	}

	content := fmt.Sprintf("✅ **服务%s成功**\n\n", actionText[action])
	content += fmt.Sprintf("🖥️ **主机**: %s (%s)\n", host.Name, host.IPAddress)
	content += fmt.Sprintf("🔧 **服务**: %s\n", service)
	content += fmt.Sprintf("⚡ **操作**: %s\n", actionText[action])
	content += fmt.Sprintf("📤 **退出码**: %d\n\n", result.ExitCode)

	if result.Output != "" {
		content += "📋 **执行结果**:\n```\n" + result.Output + "\n```\n"
	}

	return &SimplifiedIntentResponse{
		Success: result.ExitCode == 0,
		Content: content,
		Action:  "service_managed",
		Data: map[string]interface{}{
			"service":   service,
			"action":    action,
			"output":    result.Output,
			"exit_code": result.ExitCode,
		},
		Metadata: map[string]interface{}{
			"host_id":   host.ID,
			"host_ip":   host.IPAddress,
			"host_name": host.Name,
			"service":   service,
			"action":    action,
			"user_id":   userID,
		},
	}, nil
}

// executeServiceCommandOnAllHosts 在所有在线主机上执行服务命令
func (h *SSHOperationsHandler) executeServiceCommandOnAllHosts(ctx context.Context, service, action string, userID int64) (*SimplifiedIntentResponse, error) {
	var hosts []model.Host
	err := h.db.Where("status = 'online' AND deleted_at IS NULL").Find(&hosts).Error
	if err != nil {
		h.logger.WithError(err).Error("Failed to find online hosts")
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 查询在线主机失败",
			Action:  "error",
		}, nil
	}

	if len(hosts) == 0 {
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 没有找到在线的主机",
			Action:  "error",
		}, nil
	}

	// 批量执行
	results := make([]map[string]interface{}, 0, len(hosts))
	successCount := 0

	for _, host := range hosts {
		result, err := h.executeServiceCommand(ctx, &host, service, action, userID)
		if err == nil && result.Success {
			successCount++
		}

		results = append(results, map[string]interface{}{
			"host_ip":   host.IPAddress,
			"host_name": host.Name,
			"success":   result.Success,
			"content":   result.Content,
		})
	}

	actionText := map[string]string{
		"start":   "启动",
		"stop":    "停止",
		"restart": "重启",
		"status":  "查看状态",
		"enable":  "启用",
		"disable": "禁用",
	}

	content := fmt.Sprintf("📊 **批量服务%s结果**\n\n", actionText[action])
	content += fmt.Sprintf("🔧 **服务**: %s\n", service)
	content += fmt.Sprintf("✅ **成功**: %d/%d 台主机\n\n", successCount, len(hosts))

	return &SimplifiedIntentResponse{
		Success: successCount > 0,
		Content: content,
		Action:  "batch_service_managed",
		Data: map[string]interface{}{
			"service":       service,
			"action":        action,
			"total_hosts":   len(hosts),
			"success_count": successCount,
			"results":       results,
		},
		Metadata: map[string]interface{}{
			"service":       service,
			"action":        action,
			"total_hosts":   len(hosts),
			"success_count": successCount,
			"user_id":       userID,
		},
	}, nil
}

// handleFileOperation 处理文件操作
func (h *SSHOperationsHandler) handleFileOperation(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	return &SimplifiedIntentResponse{
		Success: false,
		Content: "SSH文件操作功能暂未实现",
		Action:  "not_implemented",
	}, nil
}

// handleSecurityCheck 处理安全检查
func (h *SSHOperationsHandler) handleSecurityCheck(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	return &SimplifiedIntentResponse{
		Success: false,
		Content: "SSH安全检查功能暂未实现",
		Action:  "not_implemented",
	}, nil
}

// GetSupportedOperations 获取支持的操作
func (h *SSHOperationsHandler) GetSupportedOperations() []string {
	return []string{"execute_command", "manage_service", "file_operation", "security_check"}
}

// MonitoringOperationsHandler 监控操作处理器
type MonitoringOperationsHandler struct {
	hostService HostService
	logger      *logrus.Logger
}

// NewMonitoringOperationsHandler 创建监控操作处理器
func NewMonitoringOperationsHandler(hostService HostService, logger *logrus.Logger) *MonitoringOperationsHandler {
	return &MonitoringOperationsHandler{
		hostService: hostService,
		logger:      logger,
	}
}

// Handle 处理监控操作
func (h *MonitoringOperationsHandler) Handle(ctx context.Context, intent *IntentResult, userID int64) (*SimplifiedIntentResponse, error) {
	params := intent.Parameters
	operation, _ := params["operation"].(string)

	h.logger.WithFields(logrus.Fields{
		"operation": operation,
		"user_id":   userID,
	}).Info("Processing monitoring operation")

	switch operation {
	case "system_monitor":
		return h.handleSystemMonitor(ctx, params, userID)
	case "performance_analysis":
		return h.handlePerformanceAnalysis(ctx, params, userID)
	case "log_analysis":
		return h.handleLogAnalysis(ctx, params, userID)
	case "network_diagnosis":
		return h.handleNetworkDiagnosis(ctx, params, userID)
	default:
		return &SimplifiedIntentResponse{
			Success: false,
			Content: fmt.Sprintf("不支持的监控操作：%s", operation),
			Action:  "error",
		}, nil
	}
}

// handleSystemMonitor 处理系统监控
func (h *MonitoringOperationsHandler) handleSystemMonitor(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	return &SimplifiedIntentResponse{
		Success: false,
		Content: "系统监控功能暂未实现",
		Action:  "not_implemented",
	}, nil
}

// handlePerformanceAnalysis 处理性能分析
func (h *MonitoringOperationsHandler) handlePerformanceAnalysis(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	return &SimplifiedIntentResponse{
		Success: false,
		Content: "性能分析功能暂未实现",
		Action:  "not_implemented",
	}, nil
}

// handleLogAnalysis 处理日志分析
func (h *MonitoringOperationsHandler) handleLogAnalysis(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	return &SimplifiedIntentResponse{
		Success: false,
		Content: "日志分析功能暂未实现",
		Action:  "not_implemented",
	}, nil
}

// handleNetworkDiagnosis 处理网络诊断
func (h *MonitoringOperationsHandler) handleNetworkDiagnosis(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	return &SimplifiedIntentResponse{
		Success: false,
		Content: "网络诊断功能暂未实现",
		Action:  "not_implemented",
	}, nil
}

// GetSupportedOperations 获取支持的操作
func (h *MonitoringOperationsHandler) GetSupportedOperations() []string {
	return []string{"system_monitor", "performance_analysis", "log_analysis", "network_diagnosis"}
}

// GeneralChatHandler 通用对话处理器
type GeneralChatHandler struct {
	logger *logrus.Logger
}

// NewGeneralChatHandler 创建通用对话处理器
func NewGeneralChatHandler(logger *logrus.Logger) *GeneralChatHandler {
	return &GeneralChatHandler{
		logger: logger,
	}
}

// Handle 处理通用对话
func (h *GeneralChatHandler) Handle(ctx context.Context, intent *IntentResult, userID int64) (*SimplifiedIntentResponse, error) {
	params := intent.Parameters
	intentType, _ := params["intent"].(string)

	h.logger.WithFields(logrus.Fields{
		"intent_type": intentType,
		"user_id":     userID,
	}).Info("Processing general chat")

	switch intentType {
	case "help":
		return h.handleHelp(ctx, params, userID)
	case "greeting":
		return h.handleGreeting(ctx, params, userID)
	default:
		return h.handleGeneral(ctx, params, userID)
	}
}

// handleHelp 处理帮助请求
func (h *GeneralChatHandler) handleHelp(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	content := `🤖 AI运维助手帮助信息

我可以帮您完成以下操作：

📊 **数据库操作**
• 添加主机：添加主机 ************* root password123
• 删除主机：删除192.168.1.102这个主机
• 查看主机：查看所有主机状态
• 管理告警：查看当前告警

🔧 **SSH远程操作**
• 执行命令：在*************上执行ps aux命令
• 服务管理：重启nginx服务
• 文件操作：查看/var/log/nginx/access.log文件

📈 **监控统计**
• 系统监控：检查web-01服务器的CPU使用率
• 日志分析：分析系统日志
• 性能分析：查看主机性能状态

💬 **通用对话**
• 问候交流、操作指导、系统说明等

如需具体帮助，请直接描述您想要执行的操作！`

	return &SimplifiedIntentResponse{
		Success: true,
		Content: content,
		Action:  "help",
	}, nil
}

// handleGreeting 处理问候
func (h *GeneralChatHandler) handleGreeting(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	return &SimplifiedIntentResponse{
		Success: true,
		Content: "您好！我是AI运维助手，可以帮您管理主机、执行命令、监控系统等。有什么需要帮助的吗？",
		Action:  "greeting",
	}, nil
}

// handleGeneral 处理一般对话
func (h *GeneralChatHandler) handleGeneral(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	originalInput, _ := params["original_input"].(string)
	return &SimplifiedIntentResponse{
		Success: true,
		Content: fmt.Sprintf("我理解您说的是：%s\n\n不过我主要专注于运维操作。如果您需要帮助，请输入\"帮助\"查看我能做什么。", originalInput),
		Action:  "general_chat",
	}, nil
}

// GetSupportedOperations 获取支持的操作
func (h *GeneralChatHandler) GetSupportedOperations() []string {
	return []string{"help", "greeting", "general"}
}

// isConfirmationMessage 检查是否是确认消息
func (h *DatabaseOperationsHandler) isConfirmationMessage(message string) bool {
	confirmations := []string{"确认", "确认执行", "确认删除", "是", "yes", "y", "ok", "好的", "可以", "同意", "执行"}
	lowerMsg := strings.ToLower(strings.TrimSpace(message))
	for _, conf := range confirmations {
		if lowerMsg == strings.ToLower(conf) {
			return true
		}
	}
	return false
}

// handleConfirmation 处理确认操作（内部方法）
func (h *DatabaseOperationsHandler) handleConfirmation(ctx context.Context, userID int64) (*SimplifiedIntentResponse, error) {
	h.logger.WithField("user_id", userID).Info("🔑 处理确认操作")

	h.confirmationManager.mutex.Lock()
	defer h.confirmationManager.mutex.Unlock()

	// 查找该用户的待确认操作
	var pendingOp *PendingConfirmation
	var confirmToken string
	for token, op := range h.confirmationManager.pending {
		if op.UserID == userID && time.Now().Before(op.ExpiresAt) {
			pendingOp = op
			confirmToken = token
			break
		}
	}

	if pendingOp == nil {
		h.logger.WithField("user_id", userID).Warn("没有找到待确认的操作")
		return &SimplifiedIntentResponse{
			Success: false,
			Content: "❌ 没有找到待确认的操作，或操作已过期",
			Action:  "error",
		}, nil
	}

	h.logger.WithFields(logrus.Fields{
		"user_id":       userID,
		"confirm_token": confirmToken,
		"operation":     pendingOp.Operation,
		"sql":           pendingOp.SQL,
	}).Info("🎯 找到待确认的操作，准备执行")

	// 执行待确认的操作
	h.logger.WithFields(logrus.Fields{
		"sql":         pendingOp.SQL,
		"operation":   pendingOp.Operation,
		"description": pendingOp.Description,
		"user_id":     userID,
	}).Info("🚀 执行已确认的操作")

	// 从待确认列表中移除
	delete(h.confirmationManager.pending, confirmToken)

	// 🚀 使用通用SQL执行方法
	switch pendingOp.Operation {
	case "password_update":
		return h.executePasswordUpdate(ctx, pendingOp, userID)
	default:
		// 使用通用SQL执行方法处理所有其他操作
		return h.executeConfirmedSQL(ctx, pendingOp, userID)
	}
}

// executeHostInsert 执行主机插入
func (h *DatabaseOperationsHandler) executeHostInsert(ctx context.Context, pendingOp *PendingConfirmation, userID int64) (*SimplifiedIntentResponse, error) {
	h.logger.WithFields(logrus.Fields{
		"operation":   pendingOp.Operation,
		"description": pendingOp.Description,
		"user_id":     userID,
	}).Info("Executing host insert")

	// 这里应该执行实际的主机插入逻辑
	// 模拟执行成功
	return &SimplifiedIntentResponse{
		Success: true,
		Content: "✅ **主机添加成功**\n\n🖥️ 新主机已添加到管理列表\n💡 建议测试SSH连接确保配置正确\n📝 操作已记录到审计日志",
		Action:  "host_insert_success",
		Data: map[string]interface{}{
			"operation":   "host_insert",
			"timestamp":   time.Now(),
			"description": pendingOp.Description,
		},
	}, nil
}
