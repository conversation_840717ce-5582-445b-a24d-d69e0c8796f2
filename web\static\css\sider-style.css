/* 现代化AI助手界面 - 真正的Sider风格 */

/* 全局重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Aria<PERSON>, sans-serif;
    background: #f8fafc;
    color: #1a202c;
    line-height: 1.5;
    overflow: hidden;
    font-size: 14px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 主容器 */
.sider-app {
    display: flex;
    height: 100vh;
    width: 100vw;
    background: #f8fafc;
}

/* 左侧边栏 */
.sider-sidebar {
    width: 240px;
    background: #ffffff;
    border-right: 1px solid #e2e8f0;
    display: flex;
    flex-direction: column;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
}

/* 品牌标识 */
.sider-brand {
    display: flex;
    align-items: center;
    padding: 20px 16px;
    border-bottom: 1px solid #f1f5f9;
}

.brand-icon {
    width: 28px;
    height: 28px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    margin-right: 12px;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
}

.brand-text {
    font-size: 18px;
    font-weight: 700;
    color: #1a202c;
    letter-spacing: -0.3px;
}

/* 导航菜单 */
.sider-nav {
    flex: 1;
    padding: 16px 12px;
    overflow-y: auto;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    margin-bottom: 2px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.15s ease;
    color: #718096;
    font-size: 14px;
    font-weight: 500;
}

.nav-item:hover {
    background-color: #f7fafc;
    color: #4a5568;
}

.nav-item.active {
    background: #6366f1;
    color: white;
}

.nav-item i {
    font-size: 16px;
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

/* 底部用户信息 */
.sider-user {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    border-top: 1px solid #e9ecef;
    background: #ffffff;
}

.user-avatar {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
    margin-right: 12px;
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 14px;
    font-weight: 600;
    color: #1a1a1a;
    line-height: 1.2;
}

.user-status {
    font-size: 12px;
    color: #28a745;
    line-height: 1.2;
}

/* 主内容区域 */
.sider-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f8fafc;
    position: relative;
}

/* 视图容器 */
.chat-view,
.hosts-view,
.monitoring-view,
.alerts-view,
.reports-view,
.terminal-view,
.files-view,
.settings-view {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.view-header {
    padding: 24px 32px;
    border-bottom: 1px solid #e9ecef;
    background: #ffffff;
}

.view-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 4px;
}

.view-header p {
    font-size: 14px;
    color: #6c757d;
}

.view-content {
    flex: 1;
    padding: 32px;
    overflow-y: auto;
}

.placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
    text-align: center;
}

.placeholder-content i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.placeholder-content p {
    font-size: 16px;
}



/* 聊天区域 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    background: #f8fafc;
}

/* 欢迎界面 */
.welcome-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 80px 48px;
    background: linear-gradient(135deg, #fafbfc 0%, #f3f4f6 100%);
}

.welcome-content {
    text-align: center;
    max-width: 600px;
}

.welcome-title {
    font-size: 48px;
    font-weight: 300;
    color: #1a1a1a;
    margin-bottom: 8px;
    letter-spacing: -0.02em;
}

.welcome-subtitle {
    font-size: 20px;
    color: #6c757d;
    margin-bottom: 48px;
    font-weight: 400;
}

/* 快捷建议 */
.quick-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: center;
}

.suggestion-chip {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 24px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #495057;
    text-decoration: none;
    font-weight: 500;
}

.suggestion-chip:hover {
    background: #e9ecef;
    border-color: #dee2e6;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-chip i {
    margin-right: 8px;
    font-size: 16px;
}

/* 输入区域 */
.chat-input-container {
    padding: 20px 40px 24px;
    background: #f8fafc;
    max-width: 800px;
    margin: 0 auto;
    width: 90%;
}

/* 控制栏 */
.input-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.input-wrapper {
    display: flex;
    align-items: center;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    padding: 14px 18px;
    transition: all 0.2s ease;
    min-height: 50px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.input-wrapper:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 模型选择器 */
.model-selector {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
    color: #4a5568;
}

.model-selector:hover {
    background: #f1f5f9;
    border-color: #cbd5e0;
}

.model-icon {
    width: 14px;
    height: 14px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 8px;
}

.model-name {
    font-weight: 600;
    color: #2d3748;
}

/* 模型下拉菜单 */
.model-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    max-height: 300px;
    overflow-y: auto;
    margin-top: 4px;
}

.model-dropdown-content {
    padding: 4px 0;
}

.model-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.15s ease;
    border-bottom: 1px solid #f7fafc;
}

.model-option:last-child {
    border-bottom: none;
}

.model-option:hover {
    background: #f7fafc;
}

.model-option.current {
    background: #edf2f7;
    color: #2d3748;
}

.model-option-icon {
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.model-option-info {
    flex: 1;
}

.model-option-name {
    font-size: 13px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 2px;
}

.model-option-desc {
    font-size: 11px;
    color: #718096;
    line-height: 1.3;
}

.model-option-provider {
    font-size: 10px;
    color: #a0aec0;
    background: #f7fafc;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: auto;
}

/* 输入框 */
#chat-input {
    width: 100%;
    border: none;
    background: transparent;
    resize: none;
    outline: none;
    font-size: 15px;
    line-height: 1.5;
    color: #1f2937;
    font-family: inherit;
    min-height: 40px;
    max-height: 160px;
    padding: 0;
}

#chat-input::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

/* 功能按钮 */
.input-actions {
    display: flex;
    align-items: center;
    gap: 4px;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    background: #ffffff;
    color: #718096;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 11px;
}

.action-btn:hover {
    background: #f8fafc;
    border-color: #cbd5e0;
    color: #4a5568;
}

/* 新建对话按钮 */
.action-btn.new-chat-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
    color: white;
}

.action-btn.new-chat-btn:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6a4190 100%);
}

/* 图标备选方案 - 如果Bootstrap Icons不加载 */
.action-btn .bi::before {
    font-family: "bootstrap-icons", sans-serif;
}

/* 聊天历史图标备选 */
.action-btn[title="聊天历史"] .bi:not([class*="bi-"])::before {
    content: "💬";
    font-family: "Apple Color Emoji", "Segoe UI Emoji", sans-serif;
    font-size: 10px;
}

/* 新建对话图标备选 */
.action-btn[title="新建对话"] .bi:not([class*="bi-"])::before {
    content: "➕";
    font-family: "Apple Color Emoji", "Segoe UI Emoji", sans-serif;
    font-size: 10px;
}

/* 消息样式 - 居中布局 */
.message {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 24px;
    padding: 0;
    animation: fadeInUp 0.3s ease;
    max-width: 800px;
    width: 90%;
    margin-left: auto;
    margin-right: auto;
}

/* 消息头部（AI头像和名称） */
.message-header {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 6px;
    align-self: flex-start;
    width: 100%;
}

.message-avatar {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
}

.message-sender {
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
}

.message-content {
    width: 100%;
    padding: 16px 20px;
    border-radius: 12px;
    font-size: 15px;
    line-height: 1.6;
    text-align: left;
}

.message.user .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px;
}

.message.assistant .message-content {
    background: #f8f9fa;
    color: #1a1a1a;
    border: 1px solid #e9ecef;
    border-radius: 16px;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sider-sidebar {
        width: 240px;
    }

    .sider-main {
        max-width: calc(100vw - 240px);
    }

    .welcome-title {
        font-size: 36px;
    }

    .welcome-subtitle {
        font-size: 18px;
    }

    .quick-suggestions {
        flex-direction: column;
        align-items: center;
    }

    .suggestion-chip {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .model-selector .model-name {
        display: none;
    }

    .chat-input-container {
        width: 95%;
        padding: 16px 20px;
    }

    .input-wrapper {
        padding: 16px 18px;
        min-height: 80px;
    }
}

@media (max-width: 480px) {
    .sider-sidebar {
        width: 60px;
    }

    .brand-text,
    .nav-item span {
        display: none;
    }

    .sider-user .user-info {
        display: none;
    }

    .model-selector {
        padding: 6px 8px;
    }

    .model-selector .model-name {
        display: none;
    }

    .chat-input-container {
        width: 98%;
        padding: 12px 16px;
    }

    .input-wrapper {
        padding: 12px 16px;
        min-height: 70px;
    }
}

/* 右侧聊天历史面板 */
.chat-history-panel {
    width: 320px;
    background: #ffffff;
    border-left: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
    position: fixed;
    right: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.chat-history-panel.open {
    transform: translateX(0);
}

.history-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.history-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
}

.close-history-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 8px;
    background: transparent;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
}

.close-history-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.history-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.history-search {
    padding: 16px 24px;
    border-bottom: 1px solid #e9ecef;
}

.history-search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease;
}

.history-search-input:focus {
    border-color: #667eea;
}

.history-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
}

.history-item {
    padding: 12px 24px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f8f9fa;
}

.history-item:hover {
    background: #f8f9fa;
}

.history-item-title {
    font-size: 14px;
    color: #1a1a1a;
    margin-bottom: 4px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.history-item-time {
    font-size: 12px;
    color: #6c757d;
}

.no-history {
    padding: 40px 24px;
    text-align: center;
    color: #6c757d;
    font-size: 14px;
}



/* 滚动条美化 */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
